import Header from "@/components/Header";
import NewFooter from "@/components/NewFooter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Target,
  Award,
  TrendingUp,
  Globe,
  Shield,
  Sparkles,
  CheckCircle
} from "lucide-react";

const About = () => {
  const stats = [
    { label: "Active Users", value: "50K+", icon: Users },
    { label: "Domains Analyzed", value: "2M+", icon: Globe },
    { label: "Success Rate", value: "94%", icon: Target },
    { label: "Years Experience", value: "8+", icon: Award }
  ];

  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      description: "Former domain investor with 15+ years experience",
      image: "/team-1.jpg"
    },
    {
      name: "<PERSON>",
      role: "CTO",
      description: "AI/ML expert, former Google engineer",
      image: "/team-2.jpg"
    },
    {
      name: "<PERSON>",
      role: "Head of Product",
      description: "UX specialist focused on investor tools",
      image: "/team-3.jpg"
    }
  ];

  const values = [
    {
      icon: Shield,
      title: "Trust & Security",
      description: "Your data and investments are protected with enterprise-grade security"
    },
    {
      icon: TrendingUp,
      title: "Data-Driven Insights",
      description: "AI-powered analytics provide accurate domain valuations and market trends"
    },
    {
      icon: Users,
      title: "Community First",
      description: "Building tools that empower domain investors at every level"
    },
    {
      icon: Sparkles,
      title: "Innovation",
      description: "Constantly evolving our platform with cutting-edge technology"
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="relative overflow-hidden min-h-[60vh] flex items-center bg-gradient-to-br from-primary/10 via-primary/5 to-transparent">
          <div className="container mx-auto px-4 py-20 relative z-10">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-primary/10 text-primary border-primary/20">
                About DomainSpot
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold">
                Revolutionizing Domain
                <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent block">
                  Investment Intelligence
                </span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                We're on a mission to democratize domain investing through AI-powered insights,
                making it accessible and profitable for everyone.
              </p>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-card">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <Card key={index} className="text-center border-0 shadow-soft">
                  <CardContent className="p-6">
                    <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-primary/10 mb-4">
                      <stat.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                    <div className="text-muted-foreground">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Story</h2>
                <p className="text-xl text-muted-foreground">
                  Born from the frustration of manual domain research
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <p className="text-lg text-muted-foreground">
                    DomainSpot was founded in 2016 when our CEO, Sarah Johnson, spent countless hours
                    manually researching domain investments. After missing several profitable opportunities
                    due to slow analysis, she envisioned an AI-powered platform that could instantly
                    evaluate domains.
                  </p>
                  <p className="text-lg text-muted-foreground">
                    Today, we've helped over 50,000 investors discover, evaluate, and profit from domain
                    investments using our proprietary AI algorithms and comprehensive market data.
                  </p>
                  <div className="flex items-center gap-4">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="text-muted-foreground">Trusted by professional investors worldwide</span>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="/about-hero.jpg"
                    alt="DomainSpot Story"
                    className="rounded-2xl shadow-xl"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-transparent rounded-2xl"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16 bg-card">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Values</h2>
                <p className="text-xl text-muted-foreground">
                  The principles that guide everything we do
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                {values.map((value, index) => (
                  <Card key={index} className="border-0 shadow-soft">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="p-3 rounded-lg bg-primary/10">
                          <value.icon className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg mb-2">{value.title}</h3>
                          <p className="text-muted-foreground">{value.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Meet Our Team</h2>
                <p className="text-xl text-muted-foreground">
                  The experts behind DomainSpot's success
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                {team.map((member, index) => (
                  <Card key={index} className="text-center border-0 shadow-soft">
                    <CardContent className="p-6">
                      <div className="w-24 h-24 rounded-full bg-gradient-primary mx-auto mb-4 flex items-center justify-center">
                        <Users className="h-12 w-12 text-white" />
                      </div>
                      <h3 className="font-semibold text-lg mb-1">{member.name}</h3>
                      <p className="text-primary font-medium mb-2">{member.role}</p>
                      <p className="text-sm text-muted-foreground">{member.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-2xl mx-auto space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold">
                Ready to Transform Your Domain Investing?
              </h2>
              <p className="text-xl text-white/80">
                Join thousands of successful investors using DomainSpot
              </p>
              <Button size="lg" variant="secondary" className="bg-white text-primary hover:bg-white/90">
                Get Started Today
              </Button>
            </div>
          </div>
        </section>
      </main>
      <NewFooter />
    </div>
  );
};

export default About;
