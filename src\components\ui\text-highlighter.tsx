import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface TextHighlighterProps {
  children: React.ReactNode;
  className?: string;
  trigger?: 'inView' | 'hover' | 'auto' | 'ref';
  direction?: 'left' | 'right' | 'top' | 'bottom';
  color?: string;
  duration?: number;
  delay?: number;
  as?: keyof JSX.IntrinsicElements;
  borderRadius?: string;
  forceColor?: boolean;
}

export const TextHighlighter = React.forwardRef<
  HTMLSpanElement,
  TextHighlighterProps
>(({
  children,
  className,
  trigger = 'inView',
  direction = 'left',
  color = '#0C3D91',
  duration = 0.8,
  delay = 0,
  as: Component = 'span',
  borderRadius = '4px',
  forceColor = true,
  ...props
}, ref) => {
  const [isHighlighted, setIsHighlighted] = useState(false);
  const elementRef = useRef<HTMLSpanElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    if (trigger === 'auto') {
      const timer = setTimeout(() => {
        setIsHighlighted(true);
      }, delay * 1000);
      return () => clearTimeout(timer);
    }

    if (trigger === 'inView') {
      observerRef.current = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              setIsHighlighted(true);
            }, delay * 1000);
          }
        },
        { threshold: 0.1 }
      );

      if (elementRef.current) {
        observerRef.current.observe(elementRef.current);
      }

      return () => {
        if (observerRef.current) {
          observerRef.current.disconnect();
        }
      };
    }
  }, [trigger, delay]);

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      setIsHighlighted(true);
    }
  };

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      setIsHighlighted(false);
    }
  };

  const getHighlightStyle = () => {
    const baseStyle = {
      position: 'relative' as const,
      display: 'inline-block',
    };

    const highlightStyle = {
      content: '""',
      position: 'absolute' as const,
      backgroundColor: color,
      zIndex: -1,
      transition: `all ${duration}s ease-in-out`,
    };

    switch (direction) {
      case 'left':
        return {
          ...baseStyle,
          '&::before': {
            ...highlightStyle,
            top: '0',
            left: '0',
            height: '100%',
            width: isHighlighted ? '100%' : '0%',
          }
        };
      case 'right':
        return {
          ...baseStyle,
          '&::before': {
            ...highlightStyle,
            top: '0',
            right: '0',
            height: '100%',
            width: isHighlighted ? '100%' : '0%',
          }
        };
      case 'top':
        return {
          ...baseStyle,
          '&::before': {
            ...highlightStyle,
            top: '0',
            left: '0',
            width: '100%',
            height: isHighlighted ? '100%' : '0%',
          }
        };
      case 'bottom':
        return {
          ...baseStyle,
          '&::before': {
            ...highlightStyle,
            bottom: '0',
            left: '0',
            width: '100%',
            height: isHighlighted ? '100%' : '0%',
          }
        };
      default:
        return baseStyle;
    }
  };

  const finalColor = forceColor ? color : (color || '#0C3D91');

  return (
    <span
      ref={ref || elementRef}
      className={cn(
        'relative inline-block',
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        position: 'relative',
        display: 'inline-block',
      }}
      {...props}
    >
      <span
        className="absolute inset-0 -z-10 transition-all duration-800 ease-in-out"
        style={{
          backgroundColor: finalColor,
          opacity: 0.3,
          borderRadius: borderRadius,
          transform: direction === 'left'
            ? `scaleX(${isHighlighted ? 1 : 0})`
            : direction === 'right'
              ? `scaleX(${isHighlighted ? 1 : 0})`
              : direction === 'top'
                ? `scaleY(${isHighlighted ? 1 : 0})`
                : `scaleY(${isHighlighted ? 1 : 0})`,
          transformOrigin: direction === 'left'
            ? 'left center'
            : direction === 'right'
              ? 'right center'
              : direction === 'top'
                ? 'center top'
                : 'center bottom',
          transitionDuration: `${duration}s`,
          transitionDelay: `${delay}s`,
        }}
      />
      {children}
    </span>
  );
});

TextHighlighter.displayName = 'TextHighlighter';
