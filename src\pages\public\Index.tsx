import { useState, useEffect } from "react";
import Header from "@/components/Header";
import Hero from "@/components/Hero";
import Features from "@/components/Features";
import AIShowcase from "@/components/AIShowcase";
import Partners from "@/components/Partners";
import Stats from "@/components/Stats";
import NewCTA from "@/components/NewCTA";
import NewFooter from "@/components/NewFooter";
import { PageLoader } from "@/components/ui/loading-spinner";

const Index = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        <Hero />
        <Features />
        <AIShowcase />
        <Partners />
        <Stats />
        <NewCTA />
      </main>
      <NewFooter />
    </div>
  );
};

export default Index;
