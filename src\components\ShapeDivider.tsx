import React from 'react';

interface ShapeDividerProps {
  type?: 'wave' | 'triangle' | 'curve' | 'zigzag' | 'tilt' | 'arrow';
  position?: 'top' | 'bottom';
  color?: string;
  height?: string;
  className?: string;
}

const ShapeDivider: React.FC<ShapeDividerProps> = ({
  type = 'wave',
  position = 'bottom',
  color = '#ffffff',
  height = '60px',
  className = ''
}) => {
  const getDividerPath = () => {
    switch (type) {
      case 'wave':
        return "M0,32L48,37.3C96,43,192,53,288,58.7C384,64,480,64,576,58.7C672,53,768,43,864,42.7C960,43,1056,53,1152,58.7C1248,64,1344,64,1392,64L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z";
      
      case 'triangle':
        return "M0,320L720,0L1440,320L1440,320L0,320Z";
      
      case 'curve':
        return "M0,96L1440,320L1440,320L0,320Z";
      
      case 'zigzag':
        return "M0,320L60,288L120,320L180,288L240,320L300,288L360,320L420,288L480,320L540,288L600,320L660,288L720,320L780,288L840,320L900,288L960,320L1020,288L1080,320L1140,288L1200,320L1260,288L1320,320L1380,288L1440,320L1440,320L0,320Z";
      
      case 'tilt':
        return "M0,320L1440,96L1440,320L0,320Z";
      
      case 'arrow':
        return "M0,320L720,96L1440,320L1440,320L0,320Z";
      
      default:
        return "M0,32L48,37.3C96,43,192,53,288,58.7C384,64,480,64,576,58.7C672,53,768,43,864,42.7C960,43,1056,53,1152,58.7C1248,64,1344,64,1392,64L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z";
    }
  };

  const transform = position === 'top' ? 'rotate(180deg)' : 'none';

  return (
    <div 
      className={`relative w-full overflow-hidden ${className}`}
      style={{ height, transform }}
    >
      <svg
        className="absolute bottom-0 left-0 w-full h-full"
        viewBox="0 0 1440 320"
        preserveAspectRatio="none"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d={getDividerPath()}
          fill={color}
        />
      </svg>
    </div>
  );
};

export default ShapeDivider;
