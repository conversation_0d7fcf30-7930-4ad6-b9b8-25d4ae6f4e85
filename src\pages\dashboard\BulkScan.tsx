import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardNavbar from "@/components/dashboard/DashboardNavbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  ScanLine,
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  Clock,
  Download,
  Trash2,
  Play,
  Pause
} from "lucide-react";

interface ScanResult {
  domain: string;
  status: 'analyzing' | 'completed' | 'error';
  score?: number;
  value?: string;
  category?: string;
  issues?: string[];
  recommendations?: string[];
}

const BulkScan = () => {
  const { isAuthenticated } = useAuth();
  const [domainList, setDomainList] = useState("");
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setDomainList(content);
      };
      reader.readAsText(file);
    }
  };

  const startBulkScan = async () => {
    const domains = domainList
      .split('\n')
      .map(domain => domain.trim())
      .filter(domain => domain.length > 0);

    if (domains.length === 0) return;

    setIsScanning(true);
    setScanProgress(0);
    setScanResults([]);

    // Initialize results with analyzing status
    const initialResults: ScanResult[] = domains.map(domain => ({
      domain,
      status: 'analyzing'
    }));
    setScanResults(initialResults);

    // Simulate AI scanning process
    for (let i = 0; i < domains.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate processing time

      const mockResult: ScanResult = {
        domain: domains[i],
        status: Math.random() > 0.1 ? 'completed' : 'error',
        score: Math.floor(Math.random() * 40) + 60,
        value: `$${(Math.random() * 50000 + 5000).toFixed(0)}`,
        category: ['Tech', 'E-commerce', 'Finance', 'Health', 'AI'][Math.floor(Math.random() * 5)],
        issues: Math.random() > 0.7 ? ['Long domain name', 'Contains hyphens'] : [],
        recommendations: ['Consider for investment', 'Monitor price changes']
      };

      setScanResults(prev =>
        prev.map((result, index) =>
          index === i ? mockResult : result
        )
      );

      setScanProgress(((i + 1) / domains.length) * 100);
    }

    setIsScanning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'analyzing': return <Clock className="h-4 w-4 text-yellow-600 animate-spin" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'analyzing': return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case 'completed': return "bg-green-100 text-green-800 border-green-200";
      case 'error': return "bg-red-100 text-red-800 border-red-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const exportResults = () => {
    const csvContent = [
      ['Domain', 'Status', 'Score', 'Estimated Value', 'Category', 'Issues', 'Recommendations'].join(','),
      ...scanResults.map(result => [
        result.domain,
        result.status,
        result.score || '',
        result.value || '',
        result.category || '',
        result.issues?.join('; ') || '',
        result.recommendations?.join('; ') || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'bulk-scan-results.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <DashboardNavbar
            title="Bulk Domain Scan"
            subtitle="Upload and analyze multiple domains with AI"
          />

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-2">
                  <ScanLine className="h-8 w-8 text-primary" />
                  Bulk Domain Scanner
                </h1>
                <p className="text-muted-foreground">
                  Upload a list of domains and let our AI analyze them for investment potential
                </p>
              </div>
              {scanResults.length > 0 && (
                <Button onClick={exportResults} variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Results
                </Button>
              )}
            </div>

            {/* Upload Section */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Upload Domain List
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Upload File</label>
                      <Input
                        type="file"
                        accept=".txt,.csv"
                        onChange={handleFileUpload}
                        className="cursor-pointer"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Supported formats: .txt, .csv (one domain per line)
                      </p>
                    </div>
                    {uploadedFile && (
                      <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                        <FileText className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-800">{uploadedFile.name}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setUploadedFile(null);
                            setDomainList("");
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Or Paste Domain List</label>
                      <Textarea
                        placeholder="example1.com&#10;example2.com&#10;example3.com"
                        value={domainList}
                        onChange={(e) => setDomainList(e.target.value)}
                        className="min-h-[120px]"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Enter one domain per line
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    {domainList.split('\n').filter(d => d.trim()).length} domains ready to scan
                  </div>
                  <Button
                    onClick={startBulkScan}
                    disabled={isScanning || !domainList.trim()}
                    className="bg-gradient-primary"
                  >
                    {isScanning ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        Scanning...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Start Bulk Scan
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Progress Section */}
            {isScanning && (
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold">Scanning Progress</h3>
                      <span className="text-sm text-muted-foreground">{Math.round(scanProgress)}%</span>
                    </div>
                    <Progress value={scanProgress} className="w-full" />
                    <p className="text-sm text-muted-foreground">
                      AI is analyzing your domains for investment potential...
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Results Section */}
            {scanResults.length > 0 && (
              <Card className="border-0 shadow-soft">
                <CardHeader>
                  <CardTitle>Scan Results</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {scanResults.map((result, index) => (
                      <div key={index}>
                        {/* Subtle top border for visual separation */}
                        {index > 0 && <div className="border-t border-border/30 mb-2"></div>}

                        <div className="flex items-center justify-between p-3 hover:bg-muted/30 transition-all rounded-lg">
                          <div className="flex items-center gap-4">
                            {getStatusIcon(result.status)}
                            <div>
                              <h3 className="font-semibold">{result.domain}</h3>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge className={getStatusColor(result.status)} variant="outline">
                                  {result.status}
                                </Badge>
                                {result.score && (
                                  <span className="text-sm text-muted-foreground">
                                    Score: {result.score}/100
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          {result.status === 'completed' && (
                            <div className="flex items-center gap-6">
                              <div className="text-right">
                                <p className="font-bold">{result.value}</p>
                                <p className="text-sm text-muted-foreground">{result.category}</p>
                              </div>
                              <Button variant="outline" size="sm">
                                View Details
                              </Button>
                            </div>
                          )}
                        </div>

                        {/* Subtle bottom border for visual separation */}
                        {index < scanResults.length - 1 && <div className="border-b border-border/30 mt-2"></div>}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default BulkScan;
