import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const NewCTA = () => {
  const navigate = useNavigate();

  return (
    <section className="relative overflow-hidden bg-[#0C3D91] rounded-b-[550px]">
      <div className="container mx-auto px-6 py-16 md:py-20 relative z-10">
        <div className="text-center max-w-4xl mx-auto flex flex-col items-center justify-center">
          {/* Headline */}
          <h2 className="text-3xl md:text-5xl font-bold text-white text-center leading-tight mb-6">
            Ready to Transform Your Domain Investing?
          </h2>

          {/* Subheadline */}
          <p className="text-base md:text-lg text-blue-100 text-center max-w-2xl mx-auto mb-8">
            Join thousands of investors already using AI to discover, evaluate, and sell domains more profitably than ever before.
          </p>

          {/* Primary CTA Button */}
          <div className="mb-6">
            <Button
              className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold px-8 py-3 rounded-full hover:opacity-90 transition-all"
              onClick={() => navigate("/auth")}
            >
              Join 5000+ Investors →
            </Button>
          </div>

          {/* Guarantee & Support Row */}
          <div className="flex justify-center items-center text-sm text-blue-100 mt-6 space-x-6">
            <span>15 days money back guarantee</span>
            <span>•</span>
            <span>24/7 Customer Support</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewCTA;
