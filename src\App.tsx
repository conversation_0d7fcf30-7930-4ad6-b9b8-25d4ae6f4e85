import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import ClickSpark from "@/components/ui/click-spark";
// Public pages
import Index from "./pages/public/Index";
import AuthPage from "./pages/public/AuthPage";
import Pricing from "./pages/public/Pricing";
import About from "./pages/public/About";
import Privacy from "./pages/public/Privacy";
import Contact from "./pages/public/Contact";
import Terms from "./pages/public/Terms";
import NotFound from "./pages/public/NotFound";

// Dashboard pages
import DomainSearch from "./pages/dashboard/DomainSearch";
import BulkScan from "./pages/dashboard/BulkScan";
import Analytics from "./pages/dashboard/Analytics";
import Watchlist from "./pages/dashboard/Watchlist";
import FindBuyer from "./pages/dashboard/FindBuyer";
import Settings from "./pages/dashboard/Settings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <ClickSpark className="min-h-screen">
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/auth" element={<AuthPage />} />
              <Route path="/dashboard" element={<Navigate to="/dashboard/analytics" replace />} />
              <Route path="/dashboard/search" element={<DomainSearch />} />
              <Route path="/dashboard/bulk-scan" element={<BulkScan />} />
              <Route path="/dashboard/analytics" element={<Analytics />} />
              <Route path="/dashboard/watchlist" element={<Watchlist />} />
              <Route path="/dashboard/find-buyer" element={<FindBuyer />} />

              <Route path="/dashboard/settings" element={<Settings />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/about" element={<About />} />
              <Route path="/privacy" element={<Privacy />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/terms" element={<Terms />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </ClickSpark>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
