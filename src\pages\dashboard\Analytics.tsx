import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardNavbar from "@/components/dashboard/DashboardNavbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TrendingUp, TrendingDown, BarChart3, PieChart, Activity, DollarSign, ExternalLink, Calendar } from "lucide-react";
import { InlineLoader } from "@/components/ui/loading-spinner";

const Analytics = () => {
  const { isAuthenticated } = useAuth();
  const [timeRange, setTimeRange] = useState("30d");
  const [isLoading, setIsLoading] = useState(true);

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  useEffect(() => {
    // Simulate loading analytics data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1200);

    return () => clearTimeout(timer);
  }, [timeRange]);

  const analyticsData = {
    totalValue: "$124,500",
    totalDomains: 45,
    avgValue: "$2,767",
    growth: "+12.5%",
    topPerformers: [
      { domain: "techstartup.ai", value: "$15,000", change: "+25%" },
      { domain: "blockchain-tools.com", value: "$12,800", change: "+18%" },
      { domain: "aivoice.io", value: "$9,500", change: "+32%" }
    ],
    categories: [
      { name: "AI/Tech", percentage: 35, value: "$43,575" },
      { name: "E-commerce", percentage: 25, value: "$31,125" },
      { name: "Finance", percentage: 20, value: "$24,900" },
      { name: "Health", percentage: 15, value: "$18,675" },
      { name: "Other", percentage: 5, value: "$6,225" }
    ]
  };

  const userDomains = [
    {
      domain: "techstartup.ai",
      category: "AI/Tech",
      value: "$15,000",
      acquired: "2024-01-15",
      status: "Active",
      traffic: "2.3K/mo"
    },
    {
      domain: "blockchain-tools.com",
      category: "Finance",
      value: "$12,800",
      acquired: "2024-02-08",
      status: "Active",
      traffic: "4.1K/mo"
    },
    {
      domain: "aivoice.io",
      category: "AI/Tech",
      value: "$9,500",
      acquired: "2024-03-12",
      status: "For Sale",
      traffic: "1.8K/mo"
    },
    {
      domain: "healthtech.io",
      category: "Health",
      value: "$8,200",
      acquired: "2024-01-28",
      status: "Active",
      traffic: "5.7K/mo"
    },
    {
      domain: "ecommerce-pro.com",
      category: "E-commerce",
      value: "$11,300",
      acquired: "2024-02-20",
      status: "Developing",
      traffic: "3.2K/mo"
    }
  ];

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <DashboardNavbar
            title="Analytics"
            subtitle="Portfolio performance insights"
          />

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">Portfolio Analytics</h1>
                <p className="text-muted-foreground">
                  Detailed insights and performance metrics for your domain portfolio
                </p>
              </div>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Key Metrics */}
            {isLoading ? (
              <InlineLoader message="Loading analytics data..." />
            ) : (
              <>
                <div className="grid md:grid-cols-4 gap-6">
                  <Card className="border-0 shadow-soft bg-gradient-to-br from-blue-50 to-blue-100/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Total Portfolio Value</p>
                          <p className="text-2xl font-bold">{analyticsData.totalValue}</p>
                        </div>
                        <DollarSign className="h-8 w-8 text-primary" />
                      </div>
                      <div className="flex items-center gap-1 mt-2">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-600">{analyticsData.growth}</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-soft bg-gradient-to-br from-green-50 to-green-100/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Total Domains</p>
                          <p className="text-2xl font-bold">{analyticsData.totalDomains}</p>
                        </div>
                        <BarChart3 className="h-8 w-8 text-primary" />
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">Across all categories</p>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-soft bg-gradient-to-br from-purple-50 to-purple-100/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Average Value</p>
                          <p className="text-2xl font-bold">{analyticsData.avgValue}</p>
                        </div>
                        <Activity className="h-8 w-8 text-primary" />
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">Per domain</p>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-soft bg-gradient-to-br from-orange-50 to-orange-100/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground">Growth Rate</p>
                          <p className="text-2xl font-bold text-green-600">{analyticsData.growth}</p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-green-600" />
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">Last 30 days</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Main Content Grid */}
                <div className="grid lg:grid-cols-3 gap-8">
                  {/* User Domains Table */}
                  <div className="lg:col-span-2">
                    <Card className="border-0 shadow-soft">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <BarChart3 className="h-5 w-5" />
                          Your Domain Portfolio
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Domain</TableHead>
                              <TableHead>Category</TableHead>
                              <TableHead>Value</TableHead>
                              <TableHead>Acquired</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Traffic</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {userDomains.map((domain, index) => (
                              <TableRow key={index} className="hover:bg-muted/50">
                                <TableCell className="font-medium">{domain.domain}</TableCell>
                                <TableCell>
                                  <Badge variant="outline">{domain.category}</Badge>
                                </TableCell>
                                <TableCell className="font-semibold text-green-600">{domain.value}</TableCell>
                                <TableCell className="text-muted-foreground">
                                  <div className="flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    {domain.acquired}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant={domain.status === 'Active' ? 'default' : domain.status === 'For Sale' ? 'destructive' : 'secondary'}
                                  >
                                    {domain.status}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-muted-foreground">{domain.traffic}</TableCell>
                                <TableCell>
                                  <Button variant="ghost" size="sm">
                                    <ExternalLink className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Portfolio Breakdown - Smaller Right Card */}
                  <div className="lg:col-span-1">
                    <Card className="border-0 shadow-soft">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <PieChart className="h-5 w-5" />
                          Portfolio Breakdown
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {analyticsData.categories.map((category, index) => (
                          <div key={index} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{category.name}</span>
                              <div className="text-right">
                                <span className="text-sm font-bold">{category.value}</span>
                                <span className="text-xs text-muted-foreground ml-2">({category.percentage}%)</span>
                              </div>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div
                                className="bg-gradient-primary h-2 rounded-full transition-all duration-1000"
                                style={{ width: `${category.percentage}%` }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </>
            )}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Analytics;