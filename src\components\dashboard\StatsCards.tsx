import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { DollarSign, TrendingUp, Eye, Target, BarChart3 } from "lucide-react";

interface StatsCardsProps {
  analytics: {
    totalInvested: number;
    currentValue: number;
    totalProfit: number;
    roi: number;
    activeDomains: number;
    soldDomains: number;
    pendingOffers: number;
    monthlyTraffic: number;
    domainsAnalyzed: number;
  };
}

const StatsCards = ({ analytics }: StatsCardsProps) => {
  const stats = [
    {
      title: "Portfolio Value",
      value: `$${analytics.currentValue.toLocaleString()}`,
      icon: DollarSign,
      change: "+12.5%",
      description: "vs last month"
    },
    {
      title: "Total ROI",
      value: `${analytics.roi}%`,
      icon: TrendingUp,
      change: "+45.2%",
      description: "overall return"
    },
    {
      title: "Active Domains",
      value: analytics.activeDomains.toString(),
      icon: Target,
      change: `${analytics.soldDomains} sold`,
      description: "this year"
    },
    {
      title: "Domains Analyzed",
      value: analytics.domainsAnalyzed.toString(),
      icon: BarChart3,
      change: "+15 this week",
      description: "total analyzed"
    }
  ];

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index} className="border-0 shadow-soft hover:shadow-md transition-all duration-300 bg-gradient-to-br from-card to-card/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <div className="p-2 rounded-lg bg-primary/10">
              <stat.icon className="h-5 w-5 text-primary" />
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              {stat.value}
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-xs">
                <span className="text-green-600 font-semibold bg-green-50 px-2 py-1 rounded-full">
                  {stat.change}
                </span>
              </div>
              <span className="text-xs text-muted-foreground">{stat.description}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default StatsCards;