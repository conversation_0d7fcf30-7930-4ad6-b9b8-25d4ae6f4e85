import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import SearchDemo from "./SearchDemo";
import LiquidEther from "./LiquidEther";
import { TextReveal } from "@/components/ui/text-reveal";


const Hero = () => {
  return (
    <section className="relative overflow-hidden min-h-[75vh] flex items-center pt-4">
      {/* Liquid Ether Background */}
      <div className="absolute inset-0">
        <LiquidEther
          colors={['#5227FF', '#FF9FFC', '#B19EEF']}
          mouseForce={15}
          cursorSize={80}
          isViscous={false}
          viscous={20}
          iterationsViscous={16}
          iterationsPoisson={16}
          resolution={0.3}
          isBounce={false}
          autoDemo={true}
          autoSpeed={0.3}
          autoIntensity={1.5}
          takeoverDuration={0.15}
          autoResumeDelay={4000}
          autoRampDuration={0.4}
          style={{ width: '100%', height: '100%' }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12 lg:py-16 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          <div className="space-y-6 lg:space-y-8 text-center lg:text-left">
            {/* Enhanced Badge */}
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 rounded-full border border-white/20 animate-fade-in shadow-lg backdrop-blur-sm">
              <span className="text-sm font-medium text-black">AI-Powered Domain Intelligence</span>
            </div>

            <div className="space-y-4 lg:space-y-6">
              <h1 className="text-3xl sm:text-4xl lg:text-6xl xl:text-7xl font-bold leading-tight animate-slide-up" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8), -1px -1px 2px rgba(0,0,0,0.5)' }}>
                <span className="text-white block">
                  Spot, Evaluate,
                </span>
                <span className="text-white block">
                  Acquire, Resell
                </span>
              </h1>
              <div className="text-lg sm:text-xl text-black max-w-2xl mx-auto lg:mx-0 animate-slide-up delay-200" style={{ textShadow: '1px 1px 3px rgba(255,255,255,0.7), -1px -1px 1px rgba(255,255,255,0.4)' }}>
                <TextReveal
                  text="The complete AI-powered platform for domain investors. Discover valuable domains, evaluate them with precision, and connect with buyers to maximize your ROI."
                  staggerDelay={0.08}
                  wordDuration={0.6}
                  initialDelay={0.5}
                />
              </div>
            </div>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-slide-up delay-300">
              <Button
                size="lg"
                className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                Start 7-Day Free Trial
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>

            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-3 gap-4 sm:gap-8 pt-6 lg:pt-8 animate-slide-up delay-400 relative">
              <div className="text-center lg:text-left p-4 rounded-xl">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground">
                  300K+
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">Domains Analyzed</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-full"></div>
                </div>
              </div>
              <div className="text-center lg:text-left p-4 rounded-xl">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground">
                  92%
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">AI Accuracy</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-[98%]"></div>
                </div>
              </div>
              <div className="text-center lg:text-left p-4 rounded-xl">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground">
                  $1M+
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">Total Sales</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-full"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Search Demo Component */}
          <div className="relative order-first lg:order-last animate-slide-up delay-500 z-10">
            <SearchDemo />

            {/* Enhanced Background Decoration */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-primary/30 to-secondary/20 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-secondary/30 to-primary/20 rounded-full blur-xl animate-pulse delay-500"></div>
            <div className="absolute top-1/2 -left-8 w-16 h-16 bg-accent/20 rounded-full blur-lg animate-pulse delay-1000"></div>
          </div>
        </div>
      </div>


    </section>
  );
};

export default Hero;