import { Twitter, Linkedin, Instagram, Facebook } from "lucide-react";
import { useNavigate } from "react-router-dom";

const NewFooter = () => {
  const navigate = useNavigate();

  const footerLinks = {
    company: [
      { name: "Pricing", href: "/pricing" },
      { name: "Terms and Services", href: "/terms" },
      { name: "About us", href: "/about" },
      { name: "Contact us", href: "/contact" },
      { name: "Blog", href: "/blog" }
    ]
  };

  return (
    <footer className="bg-slate-50 pt-16 pb-8">
      <div className="container mx-auto px-6">
        {/* Main Footer Content */}
        <div className="text-center mb-12">
          {/* Logo */}
          <div className="mb-6">
            <h2 className="text-4xl font-bold">
              <span className="text-blue-600">Domain</span>
              <span className="text-gray-900">Spot</span>
            </h2>
          </div>

          {/* Description */}
          <p className="text-gray-600 max-w-2xl mx-auto mb-8 leading-relaxed">
            AI-powered domain intelligence platform helping investors make smarter
            decisions with real-time market data and advanced analytics.
          </p>

          {/* Navigation Links */}
          <div className="flex flex-wrap justify-center gap-8 mb-8">
            {footerLinks.company.map((link, index) => (
              <button
                key={index}
                onClick={() => navigate(link.href)}
                className="text-gray-600 hover:text-blue-600 transition-colors duration-200 font-medium"
              >
                {link.name}
              </button>
            ))}
          </div>

          {/* Social Media Icons */}
          <div className="flex justify-center gap-4 mb-8">
            <a
              href="#"
              className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-blue-600 transition-colors duration-200"
              aria-label="Instagram"
            >
              <Instagram className="h-5 w-5" />
            </a>
            <a
              href="#"
              className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-blue-600 transition-colors duration-200"
              aria-label="Facebook"
            >
              <Facebook className="h-5 w-5" />
            </a>
            <a
              href="#"
              className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-blue-600 transition-colors duration-200"
              aria-label="Twitter"
            >
              <Twitter className="h-5 w-5" />
            </a>
            <a
              href="#"
              className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-blue-600 transition-colors duration-200"
              aria-label="LinkedIn"
            >
              <Linkedin className="h-5 w-5" />
            </a>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-200 pt-6">
          <p className="text-center text-gray-500 text-sm">
            © 2024 DomainSpot. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default NewFooter;
