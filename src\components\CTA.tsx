import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";

const CTA = () => {
  const navigate = useNavigate();
  const benefits = [
    {
      text: "15 days money back guarantee",
      icon: "/money-back.png"
    },
    {
      text: "12/7 Customer Support",
      icon: "/support.png"
    }
  ];

  return (
    <section
      className="relative overflow-hidden mx-2 sm:mx-4 mb-16"
      style={{
        backgroundColor: '#2a2a72',
        backgroundImage: 'linear-gradient(315deg, #2a2a72 0%, #009ffd 74%)',
        display: 'flex',
        minHeight: '398px',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        borderStyle: 'solid',
        borderWidth: '1px',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        paddingTop: '56px',
        paddingBottom: '56px',
        paddingLeft: '56px',
        paddingRight: '56px'
      }}
    >
      {/* Video Background */}
      <div className="absolute inset-0">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover opacity-30"
        >
          <source src="/CTA.mp4" type="video/mp4" />
        </video>
        <div className="absolute inset-0 bg-gradient-primary/60"></div>
      </div>



      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Content */}
          <div className="space-y-6">
            <div className="space-y-3">
              <h2 className="text-2xl lg:text-3xl font-bold text-primary-foreground">
                Ready to Transform Your Domain Investing?
              </h2>
              <p className="text-lg text-primary-foreground/80">
                Join thousands of investors already using AI to discover, evaluate,
                and sell domains more profitably than ever before.
              </p>
            </div>

            <div className="flex justify-start">
              <Button
                size="lg"
                variant="secondary"
                className="hover:shadow-xl hover:scale-105 transition-all duration-300"
                style={{
                  color: 'var(--e-global-color-400597b)',
                  backgroundImage: 'radial-gradient(at bottom center, var(--e-global-color-5a04e6f) 0%, var(--e-global-color-e3c75ea) 75%)',
                  borderStyle: 'solid',
                  borderWidth: '1px',
                  borderColor: 'var(--e-global-color-43938eb)',
                  borderRadius: '100px',
                  padding: '12px 24px'
                }}
                onClick={() => navigate("/auth")}
              >
                Join 5000+ Investors
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>

            <div className="flex flex-wrap gap-6 pt-2">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-2 text-primary-foreground/80">
                  <img
                    src={benefit.icon}
                    alt={benefit.text}
                    className="h-6 w-6 object-contain"
                    onError={(e) => {
                      // Fallback to CheckCircle if image fails to load
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.add('ml-6');
                    }}
                  />
                  <CheckCircle className="h-6 w-6 hidden" />
                  <span className="text-sm">{benefit.text}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Right side - Image Placeholder */}
          <div className="relative">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 aspect-video flex items-center justify-center">
              <div className="text-center text-white/60">
                <div className="w-16 h-16 bg-white/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-sm font-medium">Image Placeholder</p>
                <p className="text-xs opacity-75">Coming Soon</p>
              </div>
            </div>


          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;