import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Search, Sparkles, TrendingUp, DollarSign, X } from "lucide-react";
import { useNavigate } from "react-router-dom";

const SearchDemo = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const navigate = useNavigate();

  const validateDomain = (domain: string) => {
    // Check if domain contains a TLD (has at least one dot and text after it)
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain.trim());
  };

  const handleSearch = () => {
    const trimmedTerm = searchTerm.trim();
    if (!trimmedTerm) return;

    if (!validateDomain(trimmedTerm)) {
      alert("Please enter a valid domain name with TLD (e.g., example.com)");
      return;
    }

    setIsSearching(true);

    // Show loading for a realistic amount of time to simulate data fetching
    setTimeout(() => {
      setShowModal(true);
      setIsSearching(false);
    }, 3000); // 3 seconds loading time
  };

  const handleSignUp = () => {
    navigate("/auth");
  };



  return (
    <div className="relative bg-white/50 backdrop-blur-md rounded-2xl p-8 shadow-2xl border border-white/20 max-w-2xl mx-auto">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="inline-flex items-center gap-2 bg-primary/10 rounded-full px-4 py-2 mb-4">
          <Sparkles className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium text-primary">See Our Search Function in Action</span>
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Instant Domain Analysis
        </h3>
        <p className="text-gray-600">
          Enter any domain to see AI-powered insights in real-time
        </p>
      </div>

      {/* Search Bar */}
      <div className="relative mb-6">
        <div className="flex gap-3">
          <div className="relative flex-1">
            <Input
              type="text"
              placeholder=""
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 pr-4 py-3 text-lg border border-primary/20 focus:border-primary focus:ring-0 rounded-xl"
            />
            {!searchTerm && (
              <div className="absolute left-12 top-1/2 transform -translate-y-1/2 text-lg text-gray-400 pointer-events-none">
                Enter domain name with TLD
              </div>
            )}
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>
          <Button
            onClick={handleSearch}
            disabled={isSearching}
            className="px-8 py-3 bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 rounded-xl"
          >
            {isSearching ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Analyzing...
              </div>
            ) : (
              "Analyze"
            )}
          </Button>
        </div>
      </div>

      {/* Search Results Modal */}
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Domain Analysis Results for "{searchTerm}"</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Basic Results - Always Visible */}
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-green-50 rounded-xl p-4 text-center border border-green-200">
                <TrendingUp className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-green-600">8.5/10</div>
                <div className="text-xs text-green-600">SEO Score</div>
              </div>
              <div className="bg-blue-50 rounded-xl p-4 text-center border border-blue-200">
                <DollarSign className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-blue-600">$2,500</div>
                <div className="text-xs text-blue-600">Est. Value</div>
              </div>
              <div className="bg-purple-50 rounded-xl p-4 text-center border border-purple-200">
                <Sparkles className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-purple-600">High</div>
                <div className="text-xs text-purple-600">Potential</div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
              <div className="text-sm font-medium text-gray-700 mb-2">AI Analysis:</div>
              <div className="text-sm text-gray-600">
                Strong brandable domain with excellent commercial potential.
                High search volume keywords with growing market trends.
              </div>
            </div>

            {/* Blurred Premium Content - 60% of modal */}
            <div className="relative">
              <div className="blur-sm space-y-4 pointer-events-none">
                <div className="bg-yellow-50 rounded-xl p-4 border border-yellow-200">
                  <div className="text-sm font-medium text-yellow-700 mb-2">Market Trends:</div>
                  <div className="text-sm text-yellow-600">
                    Domain shows 45% increase in related searches over the past 6 months.
                    Similar domains have sold for $3,000-$5,000 in recent auctions.
                  </div>
                </div>

                <div className="bg-red-50 rounded-xl p-4 border border-red-200">
                  <div className="text-sm font-medium text-red-700 mb-2">Competition Analysis:</div>
                  <div className="text-sm text-red-600">
                    Low competition in this niche with high commercial intent keywords.
                    Backlink profile shows strong domain authority potential.
                  </div>
                </div>

                <div className="bg-indigo-50 rounded-xl p-4 border border-indigo-200">
                  <div className="text-sm font-medium text-indigo-700 mb-2">Investment Recommendation:</div>
                  <div className="text-sm text-indigo-600">
                    Strong buy signal with projected 200% ROI within 12 months.
                    Recommended listing price: $4,500-$6,000.
                  </div>
                </div>

                <div className="bg-teal-50 rounded-xl p-4 border border-teal-200">
                  <div className="text-sm font-medium text-teal-700 mb-2">Historical Data:</div>
                  <div className="text-sm text-teal-600">
                    Similar domains in this category have appreciated 150% over the last 2 years.
                    Current market conditions favor premium domain investments.
                  </div>
                </div>
              </div>

              {/* Paywall Overlay */}
              <div className="absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm rounded-xl">
                <div className="text-center p-6 bg-white rounded-xl shadow-lg border border-gray-200 max-w-sm">
                  <div className="text-lg font-bold text-gray-900 mb-2">
                    Sign up to see complete results
                  </div>
                  <div className="text-sm text-gray-600 mb-4">
                    Get full AI analysis, market trends, and investment recommendations
                  </div>
                  <Button
                    onClick={handleSignUp}
                    className="w-full bg-gradient-primary hover:shadow-glow"
                  >
                    Get Full Analysis
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Floating Elements */}
      <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary/20 rounded-full animate-pulse"></div>
      <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-secondary/30 rounded-full animate-pulse delay-1000"></div>
    </div>
  );
};

export default SearchDemo;
