import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Search,
  Settings,
  HelpCircle,
  BarChart3,
  Eye,
  ScanLine,
  LogOut,
  Crown,
  Sparkles,
  Users,
} from "lucide-react";

const navigationItems = [
  {
    title: "Analytics",
    url: "/dashboard/analytics",
    icon: BarChart3,
  },
  {
    title: "Domain Search",
    url: "/dashboard/search",
    icon: Search,
  },
  {
    title: "Bulk Scan",
    url: "/dashboard/bulk-scan",
    icon: ScanLine,
  },
  {
    title: "Watchlist",
    url: "/dashboard/watchlist",
    icon: Eye,
  },
  {
    title: "Find Buyer",
    url: "/dashboard/find-buyer",
    icon: Users,
  },

];

const settingsItems = [
  {
    title: "Settings",
    url: "/dashboard/settings",
    icon: Settings,
  },
  {
    title: "Help & Support",
    url: "/dashboard/help",
    icon: HelpCircle,
  },
];

export function DashboardSidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout, user } = useAuth();

  const isActive = (path: string) => location.pathname === path;

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  const getNavClassName = ({ isActive }: { isActive: boolean }) =>
    isActive
      ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-700 font-semibold rounded-lg shadow-md border border-blue-200 backdrop-blur-sm"
      : "hover:bg-gradient-to-r hover:from-blue-500/5 hover:to-purple-500/5 text-muted-foreground hover:text-foreground transition-all duration-200 hover:rounded-lg";

  return (
    <Sidebar className="w-60">
      <SidebarContent>
        {/* Logo */}
        <div className="p-4 border-b flex justify-center">
          <img
            src="/logo.png"
            alt="DomainSpot Logo"
            style={{
              maxWidth: '100%',
              border: 'none',
              borderRadius: '0',
              boxShadow: 'none',
              width: '157px',
              aspectRatio: 'auto 157 / 40',
              height: '40px',
              userSelect: 'none',
              pointerEvents: 'none'
            }}
            onError={(e) => {
              console.error('Logo failed to load:', e);
              e.currentTarget.style.display = 'none';
            }}
            onContextMenu={(e) => e.preventDefault()}
            onDragStart={(e) => e.preventDefault()}
          />
        </div>

        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.url}
                      end
                      className={getNavClassName}
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Settings */}
        <SidebarGroup>
          <SidebarGroupLabel>Settings</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {settingsItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.url}
                      end
                      className={getNavClassName}
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Upgrade Reminder */}
        <div className="mt-auto p-4">
          <Card className="border-0 bg-gradient-to-br from-purple-50 to-blue-50 shadow-soft">
            <CardContent className="p-4 text-center space-y-3">
              <div className="flex justify-center">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full">
                  <Crown className="h-5 w-5 text-white" />
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-sm text-gray-800">Upgrade to Pro</h4>
                <p className="text-xs text-gray-600 mt-1">
                  Unlock unlimited domain analysis & premium features
                </p>
              </div>
              <Button
                size="sm"
                className="w-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
                onClick={() => navigate('/pricing')}
              >
                <Sparkles className="h-3 w-3 mr-1" />
                Upgrade Now
              </Button>
            </CardContent>
          </Card>
        </div>
      </SidebarContent>
    </Sidebar>
  );
}