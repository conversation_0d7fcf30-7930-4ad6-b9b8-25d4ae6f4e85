import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, ExternalLink, TrendingUp } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Domain {
  id: string;
  name: string;
  purchasePrice: number;
  currentValue: number;
  status: string;
  score: number;
  purchaseDate: string;
  category: string;
  traffic: number;
  offers: Array<{
    id: string;
    amount: number;
    status: string;
    date: string;
  }>;
  salePrice?: number;
  saleDate?: string;
}

interface DomainListProps {
  domains: Domain[];
}

const DomainList = ({ domains }: DomainListProps) => {
  const getStatusBadge = (status: string) => {
    const styles = {
      active: "bg-green-100 text-green-800",
      for_sale: "bg-blue-100 text-blue-800",
      sold: "bg-gray-100 text-gray-800"
    };

    return (
      <Badge variant="outline" className={styles[status as keyof typeof styles]}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-blue-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <Card className="border-0 shadow-soft">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Domain Portfolio
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-0">
          {domains.map((domain, index) => (
            <div key={domain.id}>
              {/* More obvious border for visual separation */}
              {index > 0 && <div className="border-t-2 border-gray-300 mb-1"></div>}

              <div className="flex items-center justify-between p-1 hover:bg-muted/30 transition-all duration-300 rounded-lg border-2 border-gray-200 hover:border-primary/30 mb-1">
                <div className="flex items-center space-x-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-1 mb-0.5">
                      <h3 className="font-medium text-xs text-foreground">{domain.name}</h3>
                      {getStatusBadge(domain.status)}
                      <div className="flex items-center gap-1">
                        <span className="text-xs text-muted-foreground">Score:</span>
                        <span className={`font-medium text-xs px-1 py-0.5 rounded bg-gradient-to-r ${getScoreColor(domain.score)} bg-opacity-10`}>
                          {domain.score}
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-1 text-xs">
                      <div className="bg-gray-50 p-1 rounded text-xs">
                        <span className="text-muted-foreground block text-xs">Purchase Price</span>
                        <span className="font-medium text-foreground text-xs">${domain.purchasePrice.toLocaleString()}</span>
                      </div>
                      <div className="bg-gray-50 p-1.5 rounded">
                        <span className="text-muted-foreground block text-xs">Category</span>
                        <span className="font-medium text-foreground text-xs">{domain.category}</span>
                      </div>
                      <div className="bg-gray-50 p-1.5 rounded">
                        <span className="text-muted-foreground block text-xs">Monthly Traffic</span>
                        <span className="font-medium text-foreground text-xs">{domain.traffic.toLocaleString()}</span>
                      </div>
                      {domain.offers.length > 0 && (
                        <div className="bg-primary/10 p-1.5 rounded">
                          <span className="text-muted-foreground block text-xs">Pending Offers</span>
                          <span className="font-medium text-primary text-xs">
                            {domain.offers.filter(o => o.status === 'pending').length}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="text-right bg-gradient-to-br from-primary/5 to-primary/10 p-2 rounded-lg border border-primary/20">
                    <div className="font-bold text-sm bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                      ${domain.status === 'sold' ? domain.salePrice?.toLocaleString() : domain.currentValue.toLocaleString()}
                    </div>
                    <div className="text-xs text-muted-foreground mb-1">
                      {domain.status === 'sold' ? 'Sale Price' : 'Current Value'}
                    </div>
                    {domain.status !== 'sold' && (
                      <div className="text-sm text-green-600 font-bold bg-green-50 px-2 py-1 rounded-full">
                        +{Math.round(((domain.currentValue - domain.purchasePrice) / domain.purchasePrice) * 100)}%
                      </div>
                    )}
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      {domain.status === 'active' && (
                        <DropdownMenuItem>
                          List for Sale
                        </DropdownMenuItem>
                      )}
                      {domain.offers.length > 0 && (
                        <DropdownMenuItem>
                          View Offers ({domain.offers.length})
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem>
                        Download Report
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              {/* Subtle bottom border for visual separation */}
              {index < domains.length - 1 && <div className="border-b border-border/30 mt-3"></div>}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default DomainList;