import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, DollarSign, Target, TrendingUp } from "lucide-react";

interface Activity {
  id: string;
  type: string;
  domain: string;
  amount?: number;
  score?: number;
  date: string;
  description: string;
}

interface RecentActivityProps {
  activities: Activity[];
}

const RecentActivity = ({ activities }: RecentActivityProps) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'offer_received':
        return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'domain_evaluated':
        return <Target className="h-4 w-4 text-blue-600" />;
      case 'domain_sold':
        return <TrendingUp className="h-4 w-4 text-purple-600" />;
      default:
        return <Calendar className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActivityBadge = (type: string) => {
    const styles = {
      offer_received: "bg-green-100 text-green-800",
      domain_evaluated: "bg-blue-100 text-blue-800",
      domain_sold: "bg-purple-100 text-purple-800"
    };
    
    return (
      <Badge variant="outline" className={styles[type as keyof typeof styles] || "bg-gray-100 text-gray-800"}>
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card className="border-0 shadow-soft">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className="flex items-center space-x-4 p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex-shrink-0">
                {getActivityIcon(activity.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <p className="font-medium text-sm truncate">{activity.domain}</p>
                  {getActivityBadge(activity.type)}
                </div>
                <p className="text-sm text-muted-foreground">{activity.description}</p>
                {activity.amount && (
                  <p className="text-sm font-semibold text-primary">
                    ${activity.amount.toLocaleString()}
                  </p>
                )}
                {activity.score && (
                  <p className="text-sm font-semibold text-blue-600">
                    Score: {activity.score}/100
                  </p>
                )}
              </div>
              
              <div className="flex-shrink-0 text-xs text-muted-foreground">
                {formatDate(activity.date)}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentActivity;