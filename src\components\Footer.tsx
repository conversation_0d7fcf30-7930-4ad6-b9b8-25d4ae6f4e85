import { Twitter, Linkedin, Camera } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  const footerLinks = {
    platform: [
      { name: "Domain Search", href: "/search" },
      { name: "Bulk Analysis", href: "/bulk" },
      { name: "Market Trends", href: "/trends" },
      { name: "API Access", href: "/api" }
    ],
    company: [
      { name: "About", href: "/about" },
      { name: "Blog", href: "/blog" },
      { name: "Contact", href: "/contact" }
    ],
    legal: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" }
    ]
  };

  return (
    <footer style={{ backgroundColor: '#1B55A5' }} className="border-t border-white/10 relative overflow-hidden">
      <div className="container mx-auto px-10 py-20">

        {/* Main Footer Content */}
        <div className="grid md:grid-cols-4 gap-4 mb-6">
          {/* Logo & Description */}
          <div className="md:col-span-2">
            <div className="flex items-center mb-4">
              <img
                src="/logo.png"
                alt="DomainSpot Logo"
                style={{
                  maxWidth: '100%',
                  border: 'none',
                  borderRadius: '0',
                  boxShadow: 'none',
                  width: '157px',
                  aspectRatio: 'auto 157 / 40',
                  height: '40px',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                }}
                onContextMenu={(e) => e.preventDefault()}
                onDragStart={(e) => e.preventDefault()}
              />
            </div>
            <p className="text-white/70 mb-6 max-w-md">
              AI-powered domain intelligence platform helping investors make smarter decisions with real-time market data and advanced analytics.
            </p>
          </div>

          {/* Platform Links */}
          <div>
            <h4 className="text-white font-semibold mb-4">Platform</h4>
            <ul className="space-y-3">
              {footerLinks.platform.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-white/70 hover:text-cyan-400 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="text-white font-semibold mb-4">Company</h4>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-white/70 hover:text-cyan-400 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/10 pt-2 pb-0 flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="text-white/60 text-sm">
            © 2024 DomainSpot. All rights reserved.
          </div>

          <div className="flex items-center gap-6">
            <div className="flex items-center gap-6 text-sm">
              {footerLinks.legal.map((link) => (
                <Link
                  key={link.name}
                  to={link.href}
                  className="text-white/60 hover:text-cyan-400 transition-colors"
                >
                  {link.name}
                </Link>
              ))}
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm text-white/60 hidden sm:block">Follow us:</span>
              <div className="flex items-center gap-2">
                <a
                  href="#"
                  className="p-2 rounded-full bg-white/10 text-white/60 hover:text-cyan-400 hover:bg-white/20 transition-all duration-300 hover:scale-110"
                  aria-label="Twitter"
                >
                  <Twitter className="h-4 w-4" />
                </a>
                <a
                  href="#"
                  className="p-2 rounded-full bg-white/10 text-white/60 hover:text-cyan-400 hover:bg-white/20 transition-all duration-300 hover:scale-110"
                  aria-label="LinkedIn"
                >
                  <Linkedin className="h-4 w-4" />
                </a>
                <a
                  href="#"
                  className="p-2 rounded-full bg-white/10 text-white/60 hover:text-cyan-400 hover:bg-white/20 transition-all duration-300 hover:scale-110"
                  aria-label="Instagram"
                >
                  <Camera className="h-4 w-4" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;