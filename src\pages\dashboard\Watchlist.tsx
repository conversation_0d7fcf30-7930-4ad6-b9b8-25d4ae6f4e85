import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardNavbar from "@/components/dashboard/DashboardNavbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Eye, Search, Plus, Bell, Star, TrendingUp, Al<PERSON><PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Filter } from "lucide-react";

const Watchlist = () => {
  const { isAuthenticated } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [naturalLanguageQuery, setNaturalLanguageQuery] = useState("");
  const [showAlertSetup, setShowAlertSetup] = useState(false);
  const [alertFrequency, setAlertFrequency] = useState("daily");
  const [isNaturalLanguageMode, setIsNaturalLanguageMode] = useState(false);
  const [unifiedSearchQuery, setUnifiedSearchQuery] = useState("");

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const watchlistDomains = [
    {
      domain: "smartai.com",
      status: "available",
      currentPrice: "$25,000",
      targetPrice: "$20,000",
      lastChecked: "2 hours ago",
      alerts: 3,
      trending: true,
      score: 95
    },
    {
      domain: "voicetech.io",
      status: "auction",
      currentPrice: "$8,500",
      targetPrice: "$10,000",
      lastChecked: "1 hour ago",
      alerts: 1,
      trending: false,
      score: 88
    },
    {
      domain: "blockchain-hub.com",
      status: "sold",
      currentPrice: "$15,000",
      targetPrice: "$12,000",
      lastChecked: "1 day ago",
      alerts: 0,
      trending: false,
      score: 92
    },
    {
      domain: "cybersec.ai",
      status: "watching",
      currentPrice: "$5,200",
      targetPrice: "$8,000",
      lastChecked: "30 minutes ago",
      alerts: 2,
      trending: true,
      score: 90
    },
    {
      domain: "dataflow.tech",
      status: "available",
      currentPrice: "$18,000",
      targetPrice: "$15,000",
      lastChecked: "4 hours ago",
      alerts: 1,
      trending: false,
      score: 85
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available": return "bg-green-100 text-green-800 border-green-200";
      case "auction": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "sold": return "bg-red-100 text-red-800 border-red-200";
      case "watching": return "bg-blue-100 text-blue-800 border-blue-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleNaturalLanguageSearch = () => {
    // Simulate AI processing of natural language query
    const query = naturalLanguageQuery.toLowerCase();
    let filteredDomains = [...watchlistDomains];

    // Simple pattern matching for demo purposes
    if (query.includes('tech') || query.includes('ai')) {
      filteredDomains = filteredDomains.filter(d =>
        d.domain.includes('ai') || d.domain.includes('tech') || d.domain.includes('smart')
      );
    }

    if (query.includes('under') && query.includes('character')) {
      const match = query.match(/under (\d+) character/);
      if (match) {
        const maxLength = parseInt(match[1]);
        filteredDomains = filteredDomains.filter(d => d.domain.length <= maxLength);
      }
    }

    if (query.includes('available')) {
      filteredDomains = filteredDomains.filter(d => d.status === 'available');
    }

    if (query.includes('high score') || query.includes('score above')) {
      filteredDomains = filteredDomains.filter(d => d.score >= 90);
    }

    // For demo, we'll just show a message
    alert(`Found ${filteredDomains.length} domains matching: "${naturalLanguageQuery}"`);
  };

  const setupCustomAlert = () => {
    alert(`Custom alert set up for "${naturalLanguageQuery}" with ${alertFrequency} notifications`);
    setShowAlertSetup(false);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <DashboardNavbar
            title="Watchlist"
            subtitle="Track domains you're interested in"
          />

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-2">
                  <Eye className="h-8 w-8 text-primary" />
                  Domain Watchlist
                </h1>
                <p className="text-muted-foreground">
                  Track domains you're interested in and get notified of price changes
                </p>
              </div>
              <Button className="bg-gradient-primary">
                <Plus className="mr-2 h-4 w-4" />
                Add Domain
              </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid md:grid-cols-4 gap-6">
              <Card className="border-0 shadow-soft bg-gradient-to-br from-blue-50 to-blue-100/50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Watched</p>
                      <p className="text-2xl font-bold">{watchlistDomains.length}</p>
                    </div>
                    <Eye className="h-8 w-8 text-primary" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft bg-gradient-to-br from-yellow-50 to-yellow-100/50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Active Alerts</p>
                      <p className="text-2xl font-bold">
                        {watchlistDomains.reduce((sum, domain) => sum + domain.alerts, 0)}
                      </p>
                    </div>
                    <Bell className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft bg-gradient-to-br from-green-50 to-green-100/50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Available Now</p>
                      <p className="text-2xl font-bold">
                        {watchlistDomains.filter(d => d.status === "available").length}
                      </p>
                    </div>
                    <AlertCircle className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft bg-gradient-to-br from-purple-50 to-purple-100/50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Trending</p>
                      <p className="text-2xl font-bold">
                        {watchlistDomains.filter(d => d.trending).length}
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Enhanced Search */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  Smart Search & Alerts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Search Mode Toggle */}
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Search className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">
                      {isNaturalLanguageMode ? "Natural Language Search" : "Domain Search"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">Domain</span>
                    <Switch
                      checked={isNaturalLanguageMode}
                      onCheckedChange={setIsNaturalLanguageMode}
                    />
                    <span className="text-xs text-muted-foreground">Natural</span>
                  </div>
                </div>

                {/* Unified Search Bar */}
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder={
                        isNaturalLanguageMode
                          ? "e.g., 'tech startup domains under 10 characters' or 'available AI domains with high scores'"
                          : "Search watchlist domains..."
                      }
                      value={unifiedSearchQuery}
                      onChange={(e) => setUnifiedSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Button
                    onClick={isNaturalLanguageMode ? handleNaturalLanguageSearch : () => setSearchQuery(unifiedSearchQuery)}
                    disabled={!unifiedSearchQuery.trim()}
                    variant="outline"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                  <Dialog open={showAlertSetup} onOpenChange={setShowAlertSetup}>
                    <DialogTrigger asChild>
                      <Button
                        disabled={!unifiedSearchQuery.trim() || !isNaturalLanguageMode}
                        className="bg-gradient-primary"
                      >
                        <Bell className="mr-2 h-4 w-4" />
                        Set Alert
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create Custom Alert</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium mb-2 block">Search Query</label>
                          <Textarea
                            value={unifiedSearchQuery}
                            onChange={(e) => setUnifiedSearchQuery(e.target.value)}
                            placeholder="Describe what domains you want to be alerted about..."
                            className="min-h-[80px]"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium mb-2 block">Alert Frequency</label>
                          <Select value={alertFrequency} onValueChange={setAlertFrequency}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="daily">Daily</SelectItem>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="instant">Instant</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Switch id="email-alerts" />
                            <label htmlFor="email-alerts" className="text-sm">Email notifications</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch id="push-alerts" />
                            <label htmlFor="push-alerts" className="text-sm">Push notifications</label>
                          </div>
                        </div>
                        <div className="flex gap-2 pt-4">
                          <Button onClick={setupCustomAlert} className="flex-1 bg-gradient-primary">
                            Create Alert
                          </Button>
                          <Button variant="outline" onClick={() => setShowAlertSetup(false)}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
                <div className="text-xs text-muted-foreground">
                  Try: "available tech domains", "domains under $10k", "AI domains with score above 90"
                </div>
              </CardContent>
            </Card>

            {/* Watchlist */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Your Watchlist</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  {watchlistDomains.map((domain, index) => (
                    <div key={index}>
                      {/* Subtle top border for visual separation */}
                      {index > 0 && <div className="border-t border-border/40 mb-1"></div>}

                      <div className="flex items-center justify-between p-2 hover:bg-muted/30 transition-all rounded-lg border-b border-border/20">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            {domain.trending && <TrendingUp className="h-3 w-3 text-blue-600" />}
                            <Star className="h-3 w-3 text-yellow-500" />
                          </div>
                          <div>
                            <h3 className="font-medium text-sm">{domain.domain}</h3>
                            <div className="flex items-center gap-2 mt-0.5">
                              <Badge className={`${getStatusColor(domain.status)} text-xs px-1 py-0`} variant="outline">
                                {domain.status}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                Score: {domain.score}/100
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <p className="font-medium text-sm">{domain.currentPrice}</p>
                            <p className="text-xs text-muted-foreground">
                              Target: {domain.targetPrice}
                            </p>
                          </div>

                          <div className="text-right">
                            <div className="flex items-center gap-1">
                              <Bell className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs">{domain.alerts}</span>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {domain.lastChecked}
                            </p>
                          </div>

                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                            <Button variant="ghost" size="sm">
                              Remove
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Subtle bottom border for visual separation */}
                      {index < watchlistDomains.length - 1 && <div className="border-b border-border/30 mt-2"></div>}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    </SidebarProvider >
  );
};

export default Watchlist;