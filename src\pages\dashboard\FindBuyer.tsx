import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardNavbar from "@/components/dashboard/DashboardNavbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Search,
  Users,
  Mail,
  Phone,
  Globe,
  Building,
  MapPin,
  Star,
  Filter,
  Send,
  Eye,
  MessageSquare,
  TrendingUp,
  DollarSign
} from "lucide-react";

// Mock data for potential buyers
const mockBuyers = [
  {
    id: "1",
    name: "TechVentures Inc.",
    type: "Company",
    industry: "Technology",
    location: "San Francisco, CA",
    avatar: "",
    rating: 4.8,
    totalPurchases: 45,
    avgPurchaseValue: "$12,500",
    interests: ["AI", "SaaS", "Fintech"],
    lastActive: "2 days ago",
    email: "<EMAIL>",
    phone: "+****************",
    website: "techventures.com",
    description: "Leading technology investment firm specializing in AI and SaaS domain acquisitions."
  },
  {
    id: "2",
    name: "Sarah Chen",
    type: "Individual",
    industry: "E-commerce",
    location: "New York, NY",
    avatar: "",
    rating: 4.9,
    totalPurchases: 23,
    avgPurchaseValue: "$8,200",
    interests: ["E-commerce", "Fashion", "Lifestyle"],
    lastActive: "1 day ago",
    email: "<EMAIL>",
    phone: "+****************",
    website: "sarahchen.com",
    description: "Serial entrepreneur with focus on e-commerce and lifestyle brands."
  },
  {
    id: "3",
    name: "Digital Assets LLC",
    type: "Company",
    industry: "Investment",
    location: "Austin, TX",
    avatar: "",
    rating: 4.6,
    totalPurchases: 78,
    avgPurchaseValue: "$15,800",
    interests: ["Crypto", "Blockchain", "Finance"],
    lastActive: "5 hours ago",
    email: "<EMAIL>",
    phone: "+****************",
    website: "digitalassets.com",
    description: "Investment firm focused on digital assets and blockchain domains."
  }
];

const FindBuyer = () => {
  const { isAuthenticated } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedIndustry, setSelectedIndustry] = useState("");
  const [selectedType, setSelectedType] = useState("");
  const [filteredBuyers, setFilteredBuyers] = useState(mockBuyers);
  const [selectedBuyer, setSelectedBuyer] = useState(null);
  const [showContactForm, setShowContactForm] = useState(false);

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const handleSearch = () => {
    let filtered = mockBuyers;

    if (searchQuery) {
      filtered = filtered.filter(buyer =>
        buyer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        buyer.industry.toLowerCase().includes(searchQuery.toLowerCase()) ||
        buyer.interests.some(interest =>
          interest.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    if (selectedIndustry && selectedIndustry !== 'all') {
      filtered = filtered.filter(buyer => buyer.industry === selectedIndustry);
    }

    if (selectedType && selectedType !== 'all') {
      filtered = filtered.filter(buyer => buyer.type === selectedType);
    }

    setFilteredBuyers(filtered);
  };

  const ContactForm = ({ buyer, onClose }) => (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Send className="h-5 w-5" />
          Contact {buyer.name}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Domain Name</label>
          <Input placeholder="Enter domain name you want to sell" />
        </div>
        <div>
          <label className="text-sm font-medium mb-2 block">Asking Price</label>
          <Input placeholder="$10,000" />
        </div>
        <div>
          <label className="text-sm font-medium mb-2 block">Message</label>
          <Textarea
            placeholder="Hi, I have a premium domain that might interest you..."
            className="min-h-[100px]"
          />
        </div>
        <div className="flex gap-2">
          <Button className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500">
            <Send className="h-4 w-4 mr-2" />
            Send Message
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <DashboardNavbar
            title="Find Buyers"
            subtitle="Connect with potential domain buyers"
          />

          <main className="flex-1 container mx-auto px-6 py-8">
            <div className="space-y-6">

              {/* Search and Filters */}
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex gap-4 items-end">
                    <div className="flex-1">
                      <label className="text-sm font-medium mb-2 block">Search Buyers</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search by name, industry, or interests..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div className="w-48">
                      <label className="text-sm font-medium mb-2 block">Industry</label>
                      <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                        <SelectTrigger>
                          <SelectValue placeholder="All Industries" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Industries</SelectItem>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="E-commerce">E-commerce</SelectItem>
                          <SelectItem value="Investment">Investment</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="w-48">
                      <label className="text-sm font-medium mb-2 block">Type</label>
                      <Select value={selectedType} onValueChange={setSelectedType}>
                        <SelectTrigger>
                          <SelectValue placeholder="All Types" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="Company">Company</SelectItem>
                          <SelectItem value="Individual">Individual</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button onClick={handleSearch} className="bg-gradient-to-r from-blue-500 to-purple-500">
                      <Filter className="h-4 w-4 mr-2" />
                      Search
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Form Modal */}
              {showContactForm && selectedBuyer && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
                  <div className="max-w-md w-full">
                    <ContactForm
                      buyer={selectedBuyer}
                      onClose={() => setShowContactForm(false)}
                    />
                  </div>
                </div>
              )}

              {/* Buyers List */}
              <div className="grid gap-6">
                {filteredBuyers.map((buyer) => (
                  <Card key={buyer.id} className="border-0 shadow-soft hover:shadow-lg transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={buyer.avatar} />
                          <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-lg">
                            {buyer.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 space-y-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="text-xl font-semibold">{buyer.name}</h3>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Building className="h-4 w-4" />
                                {buyer.type} • {buyer.industry}
                                <MapPin className="h-4 w-4 ml-2" />
                                {buyer.location}
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span className="text-sm font-medium">{buyer.rating}</span>
                            </div>
                          </div>

                          <p className="text-sm text-muted-foreground">{buyer.description}</p>

                          <div className="flex flex-wrap gap-2">
                            {buyer.interests.map((interest, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {interest}
                              </Badge>
                            ))}
                          </div>

                          <div className="grid grid-cols-3 gap-4 py-3 border-t">
                            <div className="text-center">
                              <div className="text-lg font-semibold text-blue-600">{buyer.totalPurchases}</div>
                              <div className="text-xs text-muted-foreground">Purchases</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold text-green-600">{buyer.avgPurchaseValue}</div>
                              <div className="text-xs text-muted-foreground">Avg. Value</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold text-purple-600">{buyer.lastActive}</div>
                              <div className="text-xs text-muted-foreground">Last Active</div>
                            </div>
                          </div>

                          <div className="flex gap-2 pt-2">
                            <Button
                              onClick={() => {
                                setSelectedBuyer(buyer);
                                setShowContactForm(true);
                              }}
                              className="bg-gradient-to-r from-blue-500 to-purple-500"
                            >
                              <Send className="h-4 w-4 mr-2" />
                              Contact
                            </Button>
                            <Button variant="outline">
                              <Eye className="h-4 w-4 mr-2" />
                              View Profile
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredBuyers.length === 0 && (
                <Card className="border-0 shadow-soft">
                  <CardContent className="p-12 text-center">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No buyers found</h3>
                    <p className="text-muted-foreground">Try adjusting your search criteria</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default FindBuyer;
