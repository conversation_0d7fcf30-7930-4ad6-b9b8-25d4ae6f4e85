import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardNavbar from "@/components/dashboard/DashboardNavbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Search,
  Users,
  Mail,
  Phone,
  Globe,
  Building,
  MapPin,
  Star,
  Send,
  Eye,
  MessageSquare,
  TrendingUp,
  DollarSign
} from "lucide-react";

// Mock data for potential buyers
const mockBuyers = [
  {
    id: "1",
    name: "TechVentures Inc.",
    type: "Company",
    industry: "Technology",
    location: "San Francisco, CA",
    avatar: "",
    rating: 4.8,
    totalPurchases: 45,
    avgPurchaseValue: "$12,500",
    interests: ["AI", "SaaS", "Fintech"],
    lastActive: "2 days ago",
    email: "<EMAIL>",
    phone: "+****************",
    website: "techventures.com",
    description: "Leading technology investment firm specializing in AI and SaaS domain acquisitions."
  },
  {
    id: "2",
    name: "Sarah Chen",
    type: "Individual",
    industry: "E-commerce",
    location: "New York, NY",
    avatar: "",
    rating: 4.9,
    totalPurchases: 23,
    avgPurchaseValue: "$8,200",
    interests: ["E-commerce", "Fashion", "Lifestyle"],
    lastActive: "1 day ago",
    email: "<EMAIL>",
    phone: "+****************",
    website: "sarahchen.com",
    description: "Serial entrepreneur with focus on e-commerce and lifestyle brands."
  },
  {
    id: "3",
    name: "Digital Assets LLC",
    type: "Company",
    industry: "Investment",
    location: "Austin, TX",
    avatar: "",
    rating: 4.6,
    totalPurchases: 78,
    avgPurchaseValue: "$15,800",
    interests: ["Crypto", "Blockchain", "Finance"],
    lastActive: "5 hours ago",
    email: "<EMAIL>",
    phone: "+****************",
    website: "digitalassets.com",
    description: "Investment firm focused on digital assets and blockchain domains."
  }
];

const FindBuyer = () => {
  const { isAuthenticated } = useAuth();
  const [domainName, setDomainName] = useState("");
  const [selectedIndustry, setSelectedIndustry] = useState("");
  const [selectedType, setSelectedType] = useState("");
  const [filteredBuyers, setFilteredBuyers] = useState(mockBuyers);
  const [selectedBuyer, setSelectedBuyer] = useState(null);
  const [showContactForm, setShowContactForm] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const handleSearch = () => {
    let filtered = mockBuyers;

    // For domain-based search, we'll filter buyers based on their interests and industry
    // that might be relevant to the domain
    if (domainName) {
      const domainLower = domainName.toLowerCase();
      filtered = filtered.filter(buyer =>
        buyer.interests.some(interest =>
          domainLower.includes(interest.toLowerCase()) ||
          interest.toLowerCase().includes(domainLower)
        ) ||
        buyer.industry.toLowerCase().includes(domainLower) ||
        domainLower.includes(buyer.industry.toLowerCase())
      );
    }

    if (selectedIndustry && selectedIndustry !== 'all') {
      filtered = filtered.filter(buyer => buyer.industry === selectedIndustry);
    }

    if (selectedType && selectedType !== 'all') {
      filtered = filtered.filter(buyer => buyer.type === selectedType);
    }

    setFilteredBuyers(filtered);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredBuyers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentBuyers = filteredBuyers.slice(startIndex, endIndex);

  const handleShowMore = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const ContactForm = ({ buyer, onClose }) => (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Send className="h-5 w-5" />
          Contact {buyer.name}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Domain Name</label>
          <Input placeholder="Enter domain name you want to sell" />
        </div>
        <div>
          <label className="text-sm font-medium mb-2 block">Asking Price</label>
          <Input placeholder="$10,000" />
        </div>
        <div>
          <label className="text-sm font-medium mb-2 block">Message</label>
          <Textarea
            placeholder="Hi, I have a premium domain that might interest you..."
            className="min-h-[100px]"
          />
        </div>
        <div className="flex gap-2">
          <Button className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500">
            <Send className="h-4 w-4 mr-2" />
            Send Message
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <DashboardNavbar
            title="Find Buyers"
            subtitle="Connect with potential domain buyers"
          />

          <main className="flex-1 container mx-auto px-6 py-8">
            <div className="space-y-6">

              {/* Search and Filters */}
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex gap-4 items-end">
                    <div className="flex-1">
                      <label className="text-sm font-medium mb-2 block">Enter Domain Name</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="e.g., techstartup.com, aitools.ai, financeapp.io..."
                          value={domainName}
                          onChange={(e) => setDomainName(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div className="w-48">
                      <label className="text-sm font-medium mb-2 block">Industry</label>
                      <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                        <SelectTrigger>
                          <SelectValue placeholder="All Industries" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Industries</SelectItem>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="E-commerce">E-commerce</SelectItem>
                          <SelectItem value="Investment">Investment</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="w-48">
                      <label className="text-sm font-medium mb-2 block">Type</label>
                      <Select value={selectedType} onValueChange={setSelectedType}>
                        <SelectTrigger>
                          <SelectValue placeholder="All Types" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="Company">Company</SelectItem>
                          <SelectItem value="Individual">Individual</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button onClick={handleSearch} className="bg-gradient-to-r from-blue-500 to-purple-500">
                      <Search className="h-4 w-4 mr-2" />
                      Find Buyers
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Form Modal */}
              {showContactForm && selectedBuyer && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
                  <div className="max-w-md w-full">
                    <ContactForm
                      buyer={selectedBuyer}
                      onClose={() => setShowContactForm(false)}
                    />
                  </div>
                </div>
              )}

              {/* Buyers Table */}
              <Card className="border-0 shadow-soft">
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Buyer</TableHead>
                        <TableHead>Type & Industry</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead>Purchases</TableHead>
                        <TableHead>Avg. Value</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Interests</TableHead>
                        <TableHead>Last Active</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {currentBuyers.map((buyer) => (
                        <TableRow key={buyer.id} className="hover:bg-muted/50">
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={buyer.avatar} />
                                <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm">
                                  {buyer.name.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{buyer.name}</div>
                                <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                                  {buyer.description}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <Badge variant="secondary" className="text-xs">
                                <Building className="h-3 w-3 mr-1" />
                                {buyer.type}
                              </Badge>
                              <div className="text-sm text-muted-foreground">{buyer.industry}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium">{buyer.rating}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{buyer.totalPurchases}</div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium text-green-600">{buyer.avgPurchaseValue}</div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1 text-sm">
                              <MapPin className="h-3 w-3" />
                              <span className="truncate max-w-[120px]">{buyer.location}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {buyer.interests.slice(0, 2).map((interest, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs px-2 py-0">
                                  {interest}
                                </Badge>
                              ))}
                              {buyer.interests.length > 2 && (
                                <Badge variant="outline" className="text-xs px-2 py-0">
                                  +{buyer.interests.length - 2}
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{buyer.lastActive}</div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                onClick={() => {
                                  setSelectedBuyer(buyer);
                                  setShowContactForm(true);
                                }}
                                className="bg-gradient-to-r from-blue-500 to-purple-500"
                              >
                                <Send className="h-3 w-3 mr-1" />
                                Contact
                              </Button>
                              <Button size="sm" variant="outline">
                                <Eye className="h-3 w-3 mr-1" />
                                View
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {/* Show More Button */}
              {filteredBuyers.length > 0 && currentPage < totalPages && (
                <div className="flex justify-center mt-6">
                  <Button
                    onClick={handleShowMore}
                    variant="outline"
                    className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0 hover:from-blue-600 hover:to-purple-600"
                  >
                    Show More ({filteredBuyers.length - endIndex} remaining)
                  </Button>
                </div>
              )}

              {/* No Results */}
              {filteredBuyers.length === 0 && (
                <Card className="border-0 shadow-soft">
                  <CardContent className="p-12 text-center">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No potential buyers found</h3>
                    <p className="text-muted-foreground">
                      {domainName ?
                        `No buyers found for "${domainName}". Try a different domain or adjust your filters.` :
                        "Enter a domain name to find potential buyers."
                      }
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default FindBuyer;
