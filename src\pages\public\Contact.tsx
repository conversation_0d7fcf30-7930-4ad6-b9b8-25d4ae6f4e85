import Header from "@/components/Header";
import NewFooter from "@/components/NewFooter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  MessageSquare,
  HelpCircle,
  Users,
  Zap
} from "lucide-react";

const Contact = () => {
  const contactMethods = [
    {
      icon: Mail,
      title: "Email Support",
      description: "Get help via email",
      contact: "<EMAIL>",
      response: "Within 24 hours"
    },
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak with our team",
      contact: "1-800-DOMAIN-1",
      response: "Mon-Fri, 9AM-6PM EST"
    },
    {
      icon: MessageSquare,
      title: "Live Chat",
      description: "Instant messaging support",
      contact: "Available in-app",
      response: "Mon-<PERSON><PERSON>, 9AM-9PM EST"
    }
  ];

  const offices = [
    {
      city: "San Francisco",
      address: "123 Tech Street, Suite 400",
      zipcode: "San Francisco, CA 94105",
      phone: "+****************"
    },
    {
      city: "New York",
      address: "456 Business Ave, Floor 12",
      zipcode: "New York, NY 10001",
      phone: "+****************"
    },
    {
      city: "London",
      address: "789 Innovation Lane",
      zipcode: "London, UK EC1A 1BB",
      phone: "+44 20 7946 0789"
    }
  ];

  const faqs = [
    {
      question: "How accurate are your domain valuations?",
      answer: "Our AI-powered valuations have a 94% accuracy rate based on actual sales data."
    },
    {
      question: "Can I cancel my subscription anytime?",
      answer: "Yes, you can cancel your subscription at any time with no cancellation fees."
    },
    {
      question: "Do you offer enterprise solutions?",
      answer: "Yes, we provide custom enterprise solutions for large domain portfolios."
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="relative overflow-hidden py-20 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-primary/10 text-primary border-primary/20">
                Contact Us
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold">
                <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent block">
                  Get in Touch
                </span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Have questions? Need support? Our team is here to help you succeed
                with your domain investments.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Methods */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">How Can We Help?</h2>
                <p className="text-xl text-muted-foreground">
                  Choose the best way to reach our support team
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8 mb-16">
                {contactMethods.map((method, index) => (
                  <Card key={index} className="text-center border-0 shadow-soft hover:shadow-md transition-all">
                    <CardContent className="p-8">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-6">
                        <method.icon className="h-8 w-8 text-primary" />
                      </div>
                      <h3 className="text-xl font-semibold mb-2">{method.title}</h3>
                      <p className="text-muted-foreground mb-4">{method.description}</p>
                      <p className="font-medium text-primary mb-2">{method.contact}</p>
                      <p className="text-sm text-muted-foreground">{method.response}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Contact Form */}
        <section className="py-16 bg-card">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-2 gap-12">
                <div>
                  <h2 className="text-3xl font-bold mb-6">Send Us a Message</h2>
                  <p className="text-muted-foreground mb-8">
                    Fill out the form and we'll get back to you within 24 hours.
                  </p>

                  <form className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">First Name</label>
                        <Input placeholder="John" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Last Name</label>
                        <Input placeholder="Doe" />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Email</label>
                      <Input type="email" placeholder="<EMAIL>" />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Subject</label>
                      <Input placeholder="How can we help?" />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Message</label>
                      <Textarea
                        placeholder="Tell us more about your question or issue..."
                        className="min-h-[120px]"
                      />
                    </div>

                    <Button className="w-full bg-gradient-primary">
                      Send Message
                    </Button>
                  </form>
                </div>

                <div className="space-y-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Clock className="h-5 w-5 text-primary" />
                      Business Hours
                    </h3>
                    <div className="space-y-2 text-muted-foreground">
                      <p>Monday - Friday: 9:00 AM - 6:00 PM EST</p>
                      <p>Saturday: 10:00 AM - 4:00 PM EST</p>
                      <p>Sunday: Closed</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Zap className="h-5 w-5 text-primary" />
                      Quick Response
                    </h3>
                    <p className="text-muted-foreground">
                      Our average response time is under 4 hours during business hours.
                      For urgent issues, please call our support line.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Users className="h-5 w-5 text-primary" />
                      Expert Team
                    </h3>
                    <p className="text-muted-foreground">
                      Our support team consists of domain investment experts who understand
                      your needs and can provide valuable insights.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Office Locations */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">Our Offices</h2>
                <p className="text-xl text-muted-foreground">
                  Visit us at one of our global locations
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                {offices.map((office, index) => (
                  <Card key={index} className="border-0 shadow-soft">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <MapPin className="h-5 w-5 text-primary" />
                        <h3 className="text-lg font-semibold">{office.city}</h3>
                      </div>
                      <div className="space-y-2 text-muted-foreground">
                        <p>{office.address}</p>
                        <p>{office.zipcode}</p>
                        <p className="font-medium text-foreground">{office.phone}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-card">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
                <p className="text-xl text-muted-foreground">
                  Quick answers to common questions
                </p>
              </div>

              <div className="space-y-6">
                {faqs.map((faq, index) => (
                  <Card key={index} className="border-0 shadow-soft">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <HelpCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                        <div>
                          <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                          <p className="text-muted-foreground">{faq.answer}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>
      <NewFooter />
    </div>
  );
};

export default Contact;
