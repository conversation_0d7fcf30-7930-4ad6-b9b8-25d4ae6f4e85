import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, X } from "lucide-react";
import Header from "@/components/Header";
import NewFooter from "@/components/NewFooter";
import { useNavigate } from "react-router-dom";

const Pricing = () => {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");
  const navigate = useNavigate();

  const plans = [
    {
      name: "Standard",
      price: 22,
      description: "Perfect for individual domain investors getting started with AI-powered analysis.",
      features: [
        "100 domain analyses",
        "100 domain valuations",
        "100 potential buyer discovery",
        "Natural language search",
        "Strengths, weaknesses & recommendations",
        "Advanced filtering and sorting"
      ],
      popular: false,
      buttonText: "Get started",
      buttonVariant: "outline" as const,
      bgColor: "bg-white",
      textColor: "text-gray-900",
      savings: 0
    },
    {
      name: "Professional",
      price: 30,
      description: "For serious domain investors who need advanced analytics and bulk processing.",
      features: [
        "300 domain analyses",
        "300 domain valuations",
        "300 potential buyer discovery",
        "API integration with registrars",
        "All Standard features included"
      ],
      popular: true,
      buttonText: "Get started",
      buttonVariant: "default" as const,
      bgColor: "#181059",
      textColor: "text-white",
      buttonBg: "bg-gradient-to-r from-[#8645FF] to-[#6B55E7]",
      savings: 36
    },
    {
      name: "Annual",
      price: 225,
      priceUnit: "year",
      description: "Best value for committed domain investors with annual billing.",
      features: [
        "All Professional features",
        "12 months for the price of 7.5",
        "Priority customer support",
        "Early access to new features",
        "Annual strategy consultation"
      ],
      popular: false,
      buttonText: "Get started",
      buttonVariant: "outline" as const,
      bgColor: "#4E2D92",
      textColor: "text-white",
      savings: 38
    }
  ];

  const handlePlanSelect = (planName: string) => {
    if (planName === "Free") {
      navigate("/auth");
    } else if (planName === "Enterprise") {
      // Handle contact sales
      console.log("Contact sales for Enterprise plan");
    } else {
      navigate("/auth");
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      <Header />
      {/* Blue gradient background with curved bottom border */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-blue-500 to-indigo-600 h-1/2 rounded-b-[500px]">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
      </div>

      <main className="pt-16 pb-16 relative z-10">
        <div className="container mx-auto px-6">
          {/* Header Section */}
          <div className="text-center mb-16 space-y-6">
            <h1 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
              Predictable, transparent pricing
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Whether you're an individual, a small team, or a growing enterprise, we have a plan that aligns perfectly with your goals.
            </p>
            <div className="flex items-center justify-center gap-2 text-white/80 text-sm">
              <span>Home</span>
              <span>›</span>
              <span>Pricing</span>
            </div>
          </div>

          {/* Payment Security Text */}
          <div className="text-center mb-8">
            <p className="text-white/80 text-sm font-medium">
              Secure payment processing • No hidden fees
            </p>
          </div>

          {/* Pricing Cards */}
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <Card
                key={plan.name}
                className={`relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-2xl border-0 ${plan.bgColor.startsWith('#') ? '' : plan.bgColor} ${plan.popular ? "transform scale-105 shadow-2xl" : "shadow-lg"
                  }`}
                style={plan.bgColor.startsWith('#') ? { backgroundColor: plan.bgColor } : {}}
              >
                {/* Savings Badge */}
                {plan.savings > 0 && (
                  <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    Save {plan.savings}%
                  </div>
                )}

                <CardContent className="p-8 space-y-6">
                  {/* Price */}
                  <div className="space-y-2">
                    <div className="flex items-baseline gap-1">
                      <span className={`text-sm ${plan.textColor} opacity-80`}>$</span>
                      <span className={`text-5xl font-bold ${plan.textColor}`}>{plan.price}</span>
                      <span className={`text-sm ${plan.textColor} opacity-80`}>/ {plan.priceUnit || 'Month'}</span>
                    </div>
                  </div>

                  {/* Plan Name */}
                  <div className="space-y-3">
                    <h3 className={`text-2xl font-bold ${plan.textColor}`}>{plan.name}</h3>
                    <p className={`text-sm ${plan.textColor} ${plan.popular ? 'opacity-90' : 'opacity-70'} leading-relaxed`}>
                      {plan.description}
                    </p>
                  </div>

                  {/* Features */}
                  <ul className="space-y-4">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded-full flex items-center justify-center ${plan.popular ? 'bg-white/20' : 'bg-green-100'
                          }`}>
                          <Check className={`h-3 w-3 ${plan.popular ? 'text-white' : 'text-green-600'
                            }`} />
                        </div>
                        <span className={`text-sm ${plan.textColor} ${plan.popular ? 'opacity-90' : 'opacity-80'}`}>
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  {/* Button */}
                  <Button
                    className={`w-full mt-8 py-3 rounded-full font-medium transition-all duration-300 ${plan.popular && plan.buttonBg
                      ? `${plan.buttonBg} text-white hover:opacity-90 shadow-lg`
                      : plan.popular
                        ? "bg-white text-purple-600 hover:bg-gray-100 shadow-lg"
                        : "bg-blue-500 text-white hover:bg-blue-600 shadow-lg hover:shadow-xl"
                      }`}
                    onClick={() => handlePlanSelect(plan.name)}
                  >
                    {plan.buttonText}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* FAQ Section */}
          <div className="mt-20 text-center">
            <h3 className="text-2xl font-bold mb-8 text-gray-900">Frequently Asked Questions</h3>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left">
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900">Can I upgrade or downgrade anytime?</h4>
                <p className="text-gray-600 text-sm">
                  Yes, you can change your plan at any time. Changes take effect immediately and we'll prorate any differences.
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900">What's included in the free trial?</h4>
                <p className="text-gray-600 text-sm">
                  The Pro trial includes all Pro features for 14 days. No credit card required to get started.
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-white">How accurate is the AI evaluation?</h4>
                <p className="text-white/80 text-sm">
                  Our AI has a 98% accuracy rate, analyzing over 200 factors including market trends, keyword value, and historical sales data.
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-white">Do you offer refunds?</h4>
                <p className="text-white/80 text-sm">
                  Yes, we offer a 30-day money-back guarantee on all paid plans. Contact support for assistance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <NewFooter />
    </div>
  );
};

export default Pricing;