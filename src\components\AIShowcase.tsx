import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, Star, DollarSign, ChevronLeft, ChevronRight, Eye, Brain, BarChart3 } from "lucide-react";
import { useState, useEffect } from "react";

const AIShowcase = () => {
  const [currentCard, setCurrentCard] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  const showcaseCards = [
    {
      title: "AI Domain Evaluation",
      name: "techstartup.ai",
      price: "$15,000",
      score: 92,
      metrics: [
        { label: "Brand Score", value: 95, color: "text-green-600" },
        { label: "SEO Value", value: 88, color: "text-blue-600" },
        { label: "Market Demand", value: 94, color: "text-purple-600" }
      ],
      insights: [
        "High demand in AI/tech sector",
        "Premium .ai extension trending",
        "Short, memorable, brandable",
        "Similar domains sold for $10K-$25K"
      ]
    },
    {
      title: "Watchlist Management",
      name: "Your Domain Watchlist",
      price: "24 Domains",
      score: 87,
      metrics: [
        { label: "Active Alerts", value: 12, color: "text-blue-600" },
        { label: "Price Drops", value: 8, color: "text-green-600" },
        { label: "New Matches", value: 15, color: "text-purple-600" }
      ],
      insights: [
        "3 domains dropped in price this week",
        "5 new domains match your criteria",
        "Best time to buy: Next 48 hours",
        "Estimated portfolio value: $180K"
      ]
    },
    {
      title: "AI Analytics Dashboard",
      name: "Market Intelligence",
      price: "Live Data",
      score: 96,
      metrics: [
        { label: "Market Trends", value: 89, color: "text-green-600" },
        { label: "ROI Prediction", value: 94, color: "text-blue-600" },
        { label: "Risk Analysis", value: 78, color: "text-purple-600" }
      ],
      insights: [
        "Tech domains up 23% this quarter",
        "AI extensions showing strong growth",
        "Best selling categories identified",
        "Optimal pricing strategies suggested"
      ]
    }
  ];

  const nextCard = () => {
    setCurrentCard((prev) => (prev + 1) % showcaseCards.length);
  };

  const prevCard = () => {
    setCurrentCard((prev) => (prev - 1 + showcaseCards.length) % showcaseCards.length);
  };

  // Auto-play functionality
  useEffect(() => {
    if (!isPaused) {
      const interval = setInterval(() => {
        nextCard();
      }, 5000); // Change card every 5 seconds

      return () => clearInterval(interval);
    }
  }, [isPaused]);

  return (
    <section className="py-20 bg-gradient-to-br from-background via-primary/2 to-secondary/5 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/3 left-1/4 w-72 h-72 bg-primary/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/3 w-64 h-64 bg-secondary/8 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-10 right-10 w-6 h-6 bg-primary/20 rounded-full animate-float"></div>
        <div className="absolute bottom-20 left-20 w-4 h-4 bg-secondary/30 rounded-full animate-float delay-500"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge variant="outline" className="w-fit bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20 hover:shadow-md transition-all duration-300">
                AI Evaluation Engine
                <div className="w-2 h-2 bg-primary rounded-full animate-ping ml-2"></div>
              </Badge>
              <h2 className="text-3xl lg:text-5xl font-bold">
                See the
                <span className="text-foreground"> AI Magic</span>
                <br />in Action
              </h2>
              <p className="text-xl text-muted-foreground">
                Our AI analyzes hundreds of factors including market trends, comparable sales,
                SEO metrics, and brand potential to give you accurate valuations in seconds.
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <TrendingUp className="h-5 w-5 text-primary" />
                <span className="font-medium">Real-time market analysis</span>
              </div>
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-primary" />
                <span className="font-medium">Comparable sales data</span>
              </div>
              <div className="flex items-center gap-3">
                <Star className="h-5 w-5 text-primary" />
                <span className="font-medium">Brand value assessment</span>
              </div>
            </div>
          </div>

          <div
            className="relative"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
          >
            <Card className="relative z-10 shadow-glow bg-card/80 backdrop-blur-md border-0 overflow-hidden transition-all duration-500 ease-in-out">
              {/* Card shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-1000"></div>

              <CardHeader className="pb-4 relative z-10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {currentCard === 0 && <Brain className="h-5 w-5 text-primary" />}
                    {currentCard === 1 && <Eye className="h-5 w-5 text-primary" />}
                    {currentCard === 2 && <BarChart3 className="h-5 w-5 text-primary" />}
                    <CardTitle className="text-lg">{showcaseCards[currentCard].title}</CardTitle>
                  </div>
                  <Badge className="bg-gradient-primary text-primary-foreground shadow-md">
                    Score: {showcaseCards[currentCard].score}/100
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-lg font-semibold text-foreground">{showcaseCards[currentCard].name}</div>
                    <div className="text-2xl font-bold text-foreground">{showcaseCards[currentCard].price}</div>
                  </div>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="space-y-3">
                  {showcaseCards[currentCard].metrics.map((metric, index) => (
                    <div key={index} className="flex items-center justify-between group/metric hover:bg-muted/30 rounded-lg p-2 -m-2 transition-all duration-300">
                      <span className="text-sm font-medium group-hover/metric:text-primary transition-colors duration-300">{metric.label}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded-full overflow-hidden group-hover/metric:scale-105 transition-transform duration-300">
                          <div
                            className={`h-full bg-gradient-primary rounded-full transition-all duration-1000`}
                            style={{
                              width: `${metric.value}%`,
                              animationDelay: `${index * 300}ms`
                            }}
                          ></div>
                        </div>
                        <span className={`text-sm font-medium ${metric.color} group-hover/metric:scale-110 transition-transform duration-300`}>
                          {typeof metric.value === 'number' && metric.value <= 100 ? `${metric.value}%` : metric.value}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium mb-3">AI Insights</h4>
                  <ul className="space-y-2">
                    {showcaseCards[currentCard].insights.map((insight, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-muted-foreground">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                        {insight}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Navigation Controls */}
            <div className="flex items-center justify-between mt-6">
              <div className="flex items-center gap-2">
                {showcaseCards.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentCard(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentCard ? 'bg-primary w-8' : 'bg-muted-foreground/30'
                      }`}
                  />
                ))}
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={prevCard}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={nextCard}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Floating elements for visual interest */}
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-primary/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-6 -left-6 w-20 h-20 bg-gradient-primary/10 rounded-full blur-2xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIShowcase;