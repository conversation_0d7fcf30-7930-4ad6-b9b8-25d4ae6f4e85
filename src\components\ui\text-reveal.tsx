import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface TextRevealProps {
  text: string;
  className?: string;
  staggerDelay?: number;
  wordDuration?: number;
  initialDelay?: number;
  as?: keyof JSX.IntrinsicElements;
}

export const TextReveal: React.FC<TextRevealProps> = ({
  text,
  className,
  staggerDelay = 0.1,
  wordDuration = 0.8,
  initialDelay = 0.04,
  as: Component = 'div',
}) => {
  const words = text.split(' ');

  const containerVariants = {
    hidden: {},
    visible: (i = 1) => ({
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: initialDelay * i,
      },
    }),
  };

  const childVariants = {
    hidden: {
      opacity: 0,
      filter: 'blur(10px)',
      y: 20,
    },
    visible: {
      opacity: 1,
      filter: 'blur(0px)',
      y: 0,
      transition: {
        duration: wordDuration,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <motion.div
      as={Component}
      className={cn('flex flex-wrap', className)}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      custom={1}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          className="inline-block mr-2 mb-1"
          variants={childVariants}
        >
          {word}
        </motion.span>
      ))}
    </motion.div>
  );
};

export default TextReveal;
