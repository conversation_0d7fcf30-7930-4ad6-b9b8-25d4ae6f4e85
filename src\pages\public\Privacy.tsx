import Header from "@/components/Header";
import NewFooter from "@/components/NewFooter";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Shield, Lock, Eye, UserCheck } from "lucide-react";

const Privacy = () => {
  const sections = [
    {
      title: "Information We Collect",
      icon: Eye,
      content: [
        "Account information (name, email, billing details)",
        "Domain search queries and analysis requests",
        "Usage data and platform interactions",
        "Payment and subscription information",
        "Communication preferences and support interactions"
      ]
    },
    {
      title: "How We Use Your Information",
      icon: UserCheck,
      content: [
        "Provide and improve our domain analysis services",
        "Process payments and manage subscriptions",
        "Send important updates and notifications",
        "Provide customer support and assistance",
        "Analyze usage patterns to enhance user experience"
      ]
    },
    {
      title: "Data Protection & Security",
      icon: Lock,
      content: [
        "Industry-standard encryption for all data transmission",
        "Secure cloud infrastructure with regular security audits",
        "Limited access controls for employee data handling",
        "Regular security updates and vulnerability assessments",
        "Compliance with GDPR, CCPA, and other privacy regulations"
      ]
    },
    {
      title: "Your Privacy Rights",
      icon: Shield,
      content: [
        "Access and review your personal data",
        "Request corrections to inaccurate information",
        "Delete your account and associated data",
        "Opt-out of marketing communications",
        "Data portability and export options"
      ]
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="relative overflow-hidden py-20 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-primary/10 text-primary border-primary/20">
                Privacy Policy
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold">
                Your Privacy
                <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent block">
                  Matters to Us
                </span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                We're committed to protecting your personal information and being transparent
                about how we collect, use, and safeguard your data.
              </p>
              <p className="text-sm text-muted-foreground">
                Last updated: January 15, 2025
              </p>
            </div>
          </div>
        </section>

        {/* Privacy Sections */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto space-y-8">
              {sections.map((section, index) => (
                <Card key={index} className="border-0 shadow-soft">
                  <CardContent className="p-8">
                    <div className="flex items-start gap-4 mb-6">
                      <div className="p-3 rounded-lg bg-primary/10">
                        <section.icon className="h-6 w-6 text-primary" />
                      </div>
                      <h2 className="text-2xl font-bold">{section.title}</h2>
                    </div>
                    <ul className="space-y-3">
                      {section.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start gap-3">
                          <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                          <span className="text-muted-foreground">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Information */}
        <section className="py-16 bg-card">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <Card className="border-0 shadow-soft">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Data Retention & Cookies</h2>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      <strong>Data Retention:</strong> We retain your personal information only as long as necessary
                      to provide our services and comply with legal obligations. Account data is typically retained
                      for the duration of your subscription plus 30 days for account recovery purposes.
                    </p>
                    <p>
                      <strong>Cookies:</strong> We use essential cookies to ensure our platform functions properly,
                      analytics cookies to understand usage patterns, and preference cookies to remember your settings.
                      You can manage cookie preferences in your browser settings.
                    </p>
                    <p>
                      <strong>Third-Party Services:</strong> We work with trusted partners for payment processing,
                      analytics, and customer support. These partners are bound by strict data protection agreements
                      and only process data necessary for their specific services.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <Card className="border-0 shadow-soft bg-gradient-to-br from-primary/5 to-primary/10">
                <CardContent className="p-8 text-center">
                  <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h2 className="text-2xl font-bold mb-4">Questions About Your Privacy?</h2>
                  <p className="text-muted-foreground mb-6">
                    Our privacy team is here to help. Contact us with any questions about how we handle your data.
                  </p>
                  <div className="space-y-2">
                    <p className="font-medium">Privacy Officer</p>
                    <p className="text-muted-foreground"><EMAIL></p>
                    <p className="text-muted-foreground">1-800-DOMAIN-1</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Updates Notice */}
        <section className="py-8 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <p className="text-sm text-muted-foreground">
                We may update this Privacy Policy from time to time. We'll notify you of any material changes
                via email or through our platform. Your continued use of DomainSpot after such changes
                constitutes acceptance of the updated policy.
              </p>
            </div>
          </div>
        </section>
      </main>
      <NewFooter />
    </div>
  );
};

export default Privacy;
