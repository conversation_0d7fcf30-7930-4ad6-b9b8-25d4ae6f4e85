// Mock data for dashboard
export const mockDomains = [
  {
    id: "1",
    name: "techstartup.ai",
    purchasePrice: 2500,
    currentValue: 15000,
    status: "active",
    score: 92,
    purchaseDate: "2024-01-15",
    category: "Technology",
    traffic: 1200,
    offers: [
      { id: "1", amount: 12000, status: "pending", date: "2024-03-01" },
      { id: "2", amount: 8500, status: "declined", date: "2024-02-15" }
    ]
  },
  {
    id: "2",
    name: "greenbank.co",
    purchasePrice: 5000,
    currentValue: 22000,
    status: "for_sale",
    score: 88,
    purchaseDate: "2023-11-20",
    category: "Finance",
    traffic: 850,
    offers: [
      { id: "3", amount: 18000, status: "pending", date: "2024-03-05" }
    ]
  },
  {
    id: "3",
    name: "devtools.io",
    purchasePrice: 1200,
    currentValue: 8500,
    status: "sold",
    score: 85,
    purchaseDate: "2023-08-10",
    salePrice: 7800,
    saleDate: "2024-02-20",
    category: "Technology",
    traffic: 2100,
    offers: []
  },
  {
    id: "4",
    name: "healthtrack.app",
    purchasePrice: 3200,
    currentValue: 11000,
    status: "active",
    score: 79,
    purchaseDate: "2024-02-01",
    category: "Health",
    traffic: 450,
    offers: []
  },
  {
    id: "5",
    name: "cryptonews.net",
    purchasePrice: 800,
    currentValue: 4500,
    status: "for_sale",
    score: 74,
    purchaseDate: "2023-12-05",
    category: "Finance",
    traffic: 1800,
    offers: [
      { id: "4", amount: 3200, status: "pending", date: "2024-03-08" },
      { id: "5", amount: 2800, status: "declined", date: "2024-02-28" }
    ]
  }
];

export const mockAnalytics = {
  totalInvested: 12700,
  currentValue: 61000,
  totalProfit: 7800,
  roi: 385,
  activeDomains: 4,
  soldDomains: 1,
  pendingOffers: 3,
  monthlyTraffic: 6400,
  domainsAnalyzed: 127
};

export const mockRecentActivity = [
  {
    id: "1",
    type: "offer_received",
    domain: "techstartup.ai",
    amount: 12000,
    date: "2024-03-01",
    description: "New offer received"
  },
  {
    id: "2",
    type: "domain_evaluated",
    domain: "newdomain.tech",
    score: 76,
    date: "2024-02-28",
    description: "AI evaluation completed"
  },
  {
    id: "3",
    type: "offer_received",
    domain: "greenbank.co",
    amount: 18000,
    date: "2024-02-25",
    description: "New offer received"
  },
  {
    id: "4",
    type: "domain_sold",
    domain: "devtools.io",
    amount: 7800,
    date: "2024-02-20",
    description: "Domain successfully sold"
  },
  {
    id: "5",
    type: "offer_received",
    domain: "cryptonews.net",
    amount: 3200,
    date: "2024-02-15",
    description: "New offer received"
  }
];

export const mockMarketTrends = [
  { month: "Jan", aiDomains: 125, techDomains: 89, financeDomains: 67 },
  { month: "Feb", aiDomains: 142, techDomains: 96, financeDomains: 73 },
  { month: "Mar", aiDomains: 168, techDomains: 108, financeDomains: 81 }
];

export const mockSearchResults = [
  {
    id: "sr1",
    name: "aimarketing.pro",
    price: 3500,
    score: 84,
    category: "Marketing",
    expires: "2024-04-15",
    traffic: 890,
    isWatching: false,
    age: "2 years"
  },
  {
    id: "sr2",
    name: "blockchain.solutions",
    price: 8200,
    score: 91,
    category: "Technology",
    expires: "2024-03-28",
    traffic: 1560,
    isWatching: true,
    age: "5 years"
  },
  {
    id: "sr3",
    name: "ecostore.green",
    price: 2100,
    score: 78,
    category: "E-commerce",
    expires: "2024-04-02",
    traffic: 420,
    isWatching: false,
    age: "1 year"
  },
  {
    id: "sr4",
    name: "fintech.capital",
    price: 12500,
    score: 95,
    category: "Finance",
    expires: "2024-03-25",
    traffic: 2100,
    isWatching: true,
    age: "8 years"
  }
];