import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.');
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database types (based on our schema)
export interface Profile {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  plan: 'free' | 'pro' | 'enterprise';
  joined_date: string;
  last_active: string;
  settings: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface Domain {
  id: string;
  user_id: string;
  name: string;
  extension: string;
  full_domain: string;
  purchase_price?: number;
  current_value?: number;
  estimated_value?: number;
  ai_score?: number;
  category?: string;
  status: 'active' | 'sold' | 'expired' | 'pending';
  expires_at?: string;
  traffic_monthly: number;
  keywords?: string[];
  description?: string;
  notes?: string;
  is_for_sale: boolean;
  asking_price?: number;
  created_at: string;
  updated_at: string;
}

export interface WatchlistItem {
  id: string;
  user_id: string;
  domain_name: string;
  domain_extension: string;
  full_domain: string;
  estimated_value?: number;
  ai_score?: number;
  category?: string;
  expires_at?: string;
  traffic_monthly: number;
  notes?: string;
  alert_price?: number;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  sender_id: string;
  recipient_id: string;
  subject?: string;
  content: string;
  is_read: boolean;
  message_type: 'direct' | 'offer' | 'inquiry' | 'system';
  related_domain_id?: string;
  parent_message_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Conversation {
  id: string;
  participant_1_id: string;
  participant_2_id: string;
  last_message_id?: string;
  last_message_at: string;
  created_at: string;
  updated_at: string;
}

export interface DomainOffer {
  id: string;
  domain_id: string;
  buyer_id: string;
  seller_id: string;
  offer_amount: number;
  message?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'countered' | 'expired';
  expires_at: string;
  counter_offer_amount?: number;
  counter_message?: string;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  is_read: boolean;
  action_url?: string;
  related_id?: string;
  created_at: string;
}

export interface MarketTrend {
  id: string;
  category: string;
  trend_date: string;
  average_price?: number;
  total_sales: number;
  growth_percentage?: number;
  top_keywords?: string[];
  data_source?: string;
  created_at: string;
}

// Helper functions for common database operations

// Auth helpers
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) throw error;
  return user;
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  if (error) throw error;
  return data;
};

export const signUp = async (email: string, password: string, name: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        name,
      },
    },
  });
  if (error) throw error;
  return data;
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
};

// Profile helpers
export const getProfile = async (userId: string): Promise<Profile | null> => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  if (error) throw error;
  return data;
};

export const updateProfile = async (userId: string, updates: Partial<Profile>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Domain helpers
export const getUserDomains = async (userId: string): Promise<Domain[]> => {
  const { data, error } = await supabase
    .from('domains')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
};

export const addDomain = async (domain: Omit<Domain, 'id' | 'full_domain' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('domains')
    .insert(domain)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Watchlist helpers
export const getUserWatchlist = async (userId: string): Promise<WatchlistItem[]> => {
  const { data, error } = await supabase
    .from('watchlist')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
};

export const addToWatchlist = async (item: Omit<WatchlistItem, 'id' | 'full_domain' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('watchlist')
    .insert(item)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Message helpers
export const getUserMessages = async (userId: string): Promise<Message[]> => {
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
    .order('created_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
};

export const sendMessage = async (message: Omit<Message, 'id' | 'is_read' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('messages')
    .insert(message)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const markMessageAsRead = async (messageId: string) => {
  const { data, error } = await supabase
    .from('messages')
    .update({ is_read: true })
    .eq('id', messageId)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// Conversation helpers
export const getUserConversations = async (userId: string) => {
  const { data, error } = await supabase
    .from('conversations')
    .select(`
      *,
      participant_1:profiles!conversations_participant_1_id_fkey(id, name, avatar_url),
      participant_2:profiles!conversations_participant_2_id_fkey(id, name, avatar_url),
      last_message:messages(content, created_at)
    `)
    .or(`participant_1_id.eq.${userId},participant_2_id.eq.${userId}`)
    .order('last_message_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
};

// Market trends helpers
export const getMarketTrends = async (): Promise<MarketTrend[]> => {
  const { data, error } = await supabase
    .from('market_trends')
    .select('*')
    .order('trend_date', { ascending: false })
    .limit(30);
  
  if (error) throw error;
  return data || [];
};

// Real-time subscriptions
export const subscribeToMessages = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('messages')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `recipient_id=eq.${userId}`,
      },
      callback
    )
    .subscribe();
};

export const subscribeToConversations = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('conversations')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'conversations',
        filter: `participant_1_id=eq.${userId}`,
      },
      callback
    )
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'conversations',
        filter: `participant_2_id=eq.${userId}`,
      },
      callback
    )
    .subscribe();
};
