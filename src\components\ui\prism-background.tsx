import React, { useEffect, useRef } from 'react';

interface PrismBackgroundProps {
  className?: string;
  children?: React.ReactNode;
  variant?: 'cyberpunk' | 'default';
  intensity?: 'low' | 'medium' | 'high';
}

export const PrismBackground: React.FC<PrismBackgroundProps> = ({
  className = '',
  children,
  variant = 'cyberpunk',
  intensity = 'medium'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Prism particles
    const particles: Array<{
      x: number;
      y: number;
      z: number;
      vx: number;
      vy: number;
      vz: number;
      color: string;
      size: number;
      opacity: number;
    }> = [];

    const colors = variant === 'cyberpunk' 
      ? ['#ff0080', '#00ffff', '#ff8000', '#8000ff', '#00ff80', '#ff4080']
      : ['#7c3aed', '#3b82f6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'];

    const particleCount = intensity === 'low' ? 50 : intensity === 'medium' ? 100 : 150;

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        z: Math.random() * 1000,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        vz: Math.random() * 5 + 1,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.8 + 0.2
      });
    }

    let animationId: number;

    const animate = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      particles.forEach((particle, index) => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.z -= particle.vz;

        // Reset particle if it goes off screen or too close
        if (particle.z <= 0 || particle.x < 0 || particle.x > canvas.width || 
            particle.y < 0 || particle.y > canvas.height) {
          particle.x = Math.random() * canvas.width;
          particle.y = Math.random() * canvas.height;
          particle.z = 1000;
          particle.vx = (Math.random() - 0.5) * 2;
          particle.vy = (Math.random() - 0.5) * 2;
          particle.color = colors[Math.floor(Math.random() * colors.length)];
        }

        // Calculate 3D projection
        const scale = 200 / particle.z;
        const x2d = particle.x + (particle.x - canvas.width / 2) * scale;
        const y2d = particle.y + (particle.y - canvas.height / 2) * scale;
        const size = particle.size * scale;

        // Draw particle with glow effect
        ctx.save();
        ctx.globalAlpha = particle.opacity * (1 - particle.z / 1000);
        
        // Glow effect
        ctx.shadowColor = particle.color;
        ctx.shadowBlur = size * 2;
        
        // Main particle
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(x2d, y2d, size, 0, Math.PI * 2);
        ctx.fill();

        // Additional glow layers for cyberpunk variant
        if (variant === 'cyberpunk') {
          ctx.shadowBlur = size * 4;
          ctx.globalAlpha = (particle.opacity * (1 - particle.z / 1000)) * 0.3;
          ctx.beginPath();
          ctx.arc(x2d, y2d, size * 2, 0, Math.PI * 2);
          ctx.fill();
        }

        ctx.restore();

        // Draw connections between nearby particles
        particles.slice(index + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            const scale1 = 200 / particle.z;
            const scale2 = 200 / otherParticle.z;
            const x1 = particle.x + (particle.x - canvas.width / 2) * scale1;
            const y1 = particle.y + (particle.y - canvas.height / 2) * scale1;
            const x2 = otherParticle.x + (otherParticle.x - canvas.width / 2) * scale2;
            const y2 = otherParticle.y + (otherParticle.y - canvas.height / 2) * scale2;

            ctx.save();
            ctx.globalAlpha = (1 - distance / 100) * 0.2;
            ctx.strokeStyle = particle.color;
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            ctx.restore();
          }
        });
      });

      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [variant, intensity]);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)' }}
      />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default PrismBackground;
