﻿import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { useNavigate, Link } from "react-router-dom";

interface HeaderProps {
  showFloatingNavbar?: boolean;
}

const Header = ({ showFloatingNavbar = true }: HeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: "Features", href: "/#features" },
    { name: "Pricing", href: "/pricing" },
    { name: "About", href: "/about" },
    { name: "Contact Us", href: "/contact" }
  ];

  return (
    <>
      {/* Main Full-Width Navbar */}
      <header className="w-full bg-white border-b border-gray-200 z-40">
        <div className="container mx-auto px-6">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center cursor-pointer" onClick={() => navigate("/")}>
              <img
                src="/logo.png"
                alt="DomainSpot Logo"
                style={{
                  maxWidth: '100%',
                  border: 'none',
                  borderRadius: '0',
                  boxShadow: 'none',
                  width: '157px',
                  aspectRatio: 'auto 157 / 40',
                  height: '40px',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                }}
                onContextMenu={(e) => e.preventDefault()}
                onDragStart={(e) => e.preventDefault()}
              />
            </div>

            {/* Centered Navigation */}
            <nav className="hidden md:flex items-center justify-center flex-1 space-x-12">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className="text-gray-700 hover:text-primary transition-all duration-300 font-medium relative group"
                >
                  {item.name}
                  <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                </Link>
              ))}
            </nav>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center space-x-4">
              <Button variant="ghost" onClick={() => navigate("/auth")}>
                Sign In
              </Button>
              <Button onClick={() => navigate("/auth")}>
                Get Started
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>
      </header>

      {/* Floating Navbar on Scroll */}
      {showFloatingNavbar && (
        <header className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-5xl px-2 transition-all duration-300 ${isScrolled ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4 pointer-events-none'}`}>
          <div className="bg-white backdrop-blur-md border border-gray/20 rounded-3xl shadow-lg floating-navbar">
            <div className="flex items-center justify-between h-16 px-6">
              {/* Logo */}
              <div className="flex items-center cursor-pointer" onClick={() => navigate("/")}>
                <img
                  src="/logo.png"
                  alt="DomainSpot Logo"
                  style={{
                    maxWidth: '100%',
                    border: 'none',
                    borderRadius: '0',
                    boxShadow: 'none',
                    width: '157px',
                    aspectRatio: 'auto 157 / 40',
                    height: '40px',
                    userSelect: 'none',
                    pointerEvents: 'none'
                  }}
                  onError={(e) => {
                    e.currentTarget.style.display = "none";
                  }}
                  onContextMenu={(e) => e.preventDefault()}
                  onDragStart={(e) => e.preventDefault()}
                />
              </div>

              {/* Centered Navigation */}
              <nav className="hidden md:flex items-center justify-center flex-1 space-x-12">
                {navItems.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className="text-gray-700 hover:text-primary transition-all duration-300 font-medium relative group"
                  >
                    {item.name}
                    <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                ))}
              </nav>

              {/* Right side buttons */}
              <div className="hidden md:flex items-center space-x-4">
                <Button variant="ghost" className="text-gray-700 hover:text-primary hover:bg-primary/10 transition-all duration-300" onClick={() => navigate("/auth")}>
                  Sign In
                </Button>
                <Button
                  className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden text-white"
                  onClick={() => navigate("/auth")}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                  Get Started
                </Button>
              </div>

              {/* Mobile menu button (no menu icon, just hamburger) */}
              <button
                className="md:hidden p-2 rounded-lg hover:bg-primary/10 transition-colors text-gray-700"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </button>
            </div>

            {isMenuOpen && (
              <div className="md:hidden border-t border-gray/20 p-4 space-y-4">
                {navItems.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium py-2 text-center relative group"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                    <span className="absolute left-1/2 bottom-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full transform -translate-x-1/2"></span>
                  </Link>
                ))}
                <div className="flex flex-col space-y-2 pt-4 border-t border-gray/20">
                  <Button variant="ghost" className="w-full text-gray-700 hover:text-primary hover:bg-primary/10" onClick={() => navigate("/auth")}>
                    Sign In
                  </Button>
                  <Button
                    className="w-full bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden text-white"
                    onClick={() => navigate("/auth")}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                    Get Started
                  </Button>
                </div>
              </div>
            )}
          </div>
        </header>
      )}
    </>
  );
};

export default Header;
