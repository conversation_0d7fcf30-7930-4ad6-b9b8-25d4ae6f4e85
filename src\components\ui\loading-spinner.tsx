import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'white' | 'gray';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = '',
  color = 'primary'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    primary: 'border-primary border-t-transparent',
    white: 'border-white border-t-transparent',
    gray: 'border-gray-300 border-t-transparent'
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
    />
  );
};

// Animated bars loading component
export const AnimatedBarsLoader: React.FC = () => {
  const barHeights = [16, 32, 48, 40, 24]; // Different heights for each bar

  return (
    <div className="flex items-end justify-center space-x-2">
      {barHeights.map((height, index) => (
        <div
          key={index}
          className="bg-gradient-to-t from-blue-300 via-blue-500 to-blue-600 rounded-sm animate-bounce"
          style={{
            width: '12px',
            height: `${height}px`,
            animationDelay: `${index * 0.15}s`,
            animationDuration: '1.4s',
          }}
        />
      ))}
    </div>
  );
};

// Full page loading component
export const PageLoader: React.FC<{ message?: string }> = ({ message }) => {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center space-y-4">
        <AnimatedBarsLoader />
        {message && <p className="text-muted-foreground font-medium">{message}</p>}
      </div>
    </div>
  );
};

// Inline loading component
export const InlineLoader: React.FC<{ message?: string; size?: 'sm' | 'md' | 'lg' }> = ({
  message = 'Loading...',
  size = 'md'
}) => {
  return (
    <div className="flex items-center justify-center space-x-3 py-8">
      <LoadingSpinner size={size} />
      <span className="text-muted-foreground">{message}</span>
    </div>
  );
};

// Button loading state
export const ButtonLoader: React.FC<{ size?: 'sm' | 'md' }> = ({ size = 'sm' }) => {
  return <LoadingSpinner size={size} color="white" />;
};

export default LoadingSpinner;
