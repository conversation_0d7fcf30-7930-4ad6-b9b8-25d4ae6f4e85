import React, { useEffect, useRef, useState } from 'react';

interface Spark {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  size: number;
  color: string;
}

interface ClickSparkProps {
  children?: React.ReactNode;
  className?: string;
  sparkCount?: number;
  colors?: string[];
  sparkSize?: number;
  sparkLife?: number;
}

export const ClickSpark: React.FC<ClickSparkProps> = ({
  children,
  className = '',
  sparkCount = 12,
  colors = ['#ff0080', '#00ffff', '#ff8000', '#8000ff', '#00ff80'],
  sparkSize = 4,
  sparkLife = 60
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [sparks, setSparks] = useState<Spark[]>([]);
  const sparkIdRef = useRef(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = container.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const handleClick = (e: MouseEvent) => {
      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const newSparks: Spark[] = [];
      for (let i = 0; i < sparkCount; i++) {
        const angle = (Math.PI * 2 * i) / sparkCount + Math.random() * 0.5;
        const velocity = Math.random() * 8 + 4;
        
        newSparks.push({
          id: sparkIdRef.current++,
          x,
          y,
          vx: Math.cos(angle) * velocity,
          vy: Math.sin(angle) * velocity,
          life: sparkLife,
          maxLife: sparkLife,
          size: sparkSize + Math.random() * 2,
          color: colors[Math.floor(Math.random() * colors.length)]
        });
      }

      setSparks(prev => [...prev, ...newSparks]);
    };

    container.addEventListener('click', handleClick);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      container.removeEventListener('click', handleClick);
    };
  }, [sparkCount, colors, sparkSize, sparkLife]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let animationId: number;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      setSparks(prevSparks => {
        const updatedSparks = prevSparks
          .map(spark => ({
            ...spark,
            x: spark.x + spark.vx,
            y: spark.y + spark.vy,
            vx: spark.vx * 0.98, // friction
            vy: spark.vy * 0.98 + 0.2, // gravity
            life: spark.life - 1
          }))
          .filter(spark => spark.life > 0);

        // Draw sparks
        updatedSparks.forEach(spark => {
          const alpha = spark.life / spark.maxLife;
          const size = spark.size * alpha;

          ctx.save();
          ctx.globalAlpha = alpha;
          
          // Glow effect
          ctx.shadowColor = spark.color;
          ctx.shadowBlur = size * 2;
          
          // Main spark
          ctx.fillStyle = spark.color;
          ctx.beginPath();
          ctx.arc(spark.x, spark.y, size, 0, Math.PI * 2);
          ctx.fill();

          // Inner bright core
          ctx.shadowBlur = 0;
          ctx.fillStyle = '#ffffff';
          ctx.beginPath();
          ctx.arc(spark.x, spark.y, size * 0.3, 0, Math.PI * 2);
          ctx.fill();

          // Trailing effect
          ctx.globalAlpha = alpha * 0.3;
          ctx.fillStyle = spark.color;
          ctx.beginPath();
          ctx.arc(spark.x - spark.vx, spark.y - spark.vy, size * 0.5, 0, Math.PI * 2);
          ctx.fill();

          ctx.restore();
        });

        return updatedSparks;
      });

      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, []);

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full pointer-events-none z-50"
        style={{ mixBlendMode: 'screen' }}
      />
      {children}
    </div>
  );
};

export default ClickSpark;
