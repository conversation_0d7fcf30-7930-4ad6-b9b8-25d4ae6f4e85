import { Card, CardContent } from "@/components/ui/card";
import { Search, Brain, Handshake } from "lucide-react";
import { TextHighlighter } from "@/components/ui/text-highlighter";

const Features = () => {
  const features = [
    {
      icon: <Search className="h-8 w-8" style={{ color: '#6A50ED' }} />,
      title: "Smart Discovery",
      description: "AI-powered algorithms scan millions of expiring domains and identify hidden gems with high investment potential.",
      details: [
        "Real-time monitoring of domain marketplaces",
        "Advanced filtering by industry and keywords",
        "Trending domain pattern analysis"
      ]
    },
    {
      icon: <Brain className="h-8 w-8" style={{ color: '#6A50ED' }} />,
      title: "AI Evaluation",
      description: "Get instant, accurate valuations using machine learning models trained on millions of domain sales.",
      details: [
        "Market trend analysis and price predictions",
        "SEO value and traffic potential scoring",
        "Brand value and memorability assessment"
      ]
    },
    {
      icon: <Handshake className="h-8 w-8" style={{ color: '#6A50ED' }} />,
      title: "Buyer Matching",
      description: "Connect with the right buyers through our intelligent matching system and automated outreach tools.",
      details: [
        "AI-powered buyer identification",
        "Automated personalized outreach campaigns",
        "Negotiation support and deal tracking"
      ]
    }
  ];

  return (
    <section id="features" className="py-20 bg-gradient-hero relative overflow-hidden">
      {/* Background Decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-purple-200/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-blue-200/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center space-y-4 mb-16">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-card/50 backdrop-blur rounded-full border border-border shadow-sm">
            <Brain className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium text-primary">AI-Powered Features</span>
          </div>
          <h2 className="text-3xl lg:text-5xl font-bold leading-tight">
            <TextHighlighter
              trigger="inView"
              color="#0C3D91"
              direction="left"
              duration={0.8}
              delay={0.2}
              borderRadius="6px"
              forceColor={true}
            >
              Everything
            </TextHighlighter> You Need to
            <span className="block sm:inline"> Make a Profit from Selling <span className="bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent">Domains</span></span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            From discovery to sale, our comprehensive platform handles every aspect of domain investing with AI precision and market intelligence.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="group hover:shadow-soft hover:scale-105 transition-all duration-500 border-0 bg-card/50 backdrop-blur rounded-2xl overflow-hidden animate-fade-in"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="p-6 space-y-4">
                <div className="w-16 h-16 rounded-xl bg-gradient-secondary flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>

                <div className="space-y-3">
                  <h3 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-300">{feature.title}</h3>
                  <p className="text-muted-foreground text-sm leading-relaxed group-hover:text-foreground/70 transition-colors duration-300">{feature.description}</p>

                  <ul className="space-y-2">
                    {feature.details.map((detail, idx) => (
                      <li key={idx} className="flex items-start gap-2 text-xs text-muted-foreground group-hover:text-foreground/60 transition-colors duration-300">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary mt-1.5 flex-shrink-0"></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;