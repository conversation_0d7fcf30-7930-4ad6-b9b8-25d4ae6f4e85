import Header from "@/components/Header";
import NewFooter from "@/components/NewFooter";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, Shield, AlertTriangle, CheckCircle } from "lucide-react";

const Terms = () => {
  const sections = [
    {
      title: "Acceptance of Terms",
      icon: CheckCircle,
      content: [
        "By accessing and using DomainSpot, you accept and agree to be bound by these Terms of Service",
        "If you do not agree to these terms, please do not use our services",
        "We may update these terms from time to time, and continued use constitutes acceptance",
        "You must be at least 18 years old to use our services"
      ]
    },
    {
      title: "Service Description",
      icon: FileText,
      content: [
        "DomainSpot provides AI-powered domain analysis and investment tools",
        "Our platform offers domain valuations, market insights, and portfolio management",
        "Services are provided on a subscription basis with different plan tiers",
        "We reserve the right to modify or discontinue services with reasonable notice"
      ]
    },
    {
      title: "User Responsibilities",
      icon: Shield,
      content: [
        "Provide accurate and complete registration information",
        "Maintain the security of your account credentials",
        "Use the service only for lawful purposes and in accordance with these terms",
        "Not attempt to reverse engineer, hack, or compromise our systems",
        "Respect intellectual property rights and not redistribute our content"
      ]
    },
    {
      title: "Limitations & Disclaimers",
      icon: AlertTriangle,
      content: [
        "Domain valuations are estimates based on available data and algorithms",
        "We do not guarantee the accuracy of valuations or investment outcomes",
        "Users are responsible for their own investment decisions",
        "Our liability is limited to the amount paid for services in the preceding 12 months",
        "Services are provided 'as is' without warranties of any kind"
      ]
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="relative overflow-hidden py-20 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-primary/10 text-primary border-primary/20">
                Terms of Service
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold">
                Terms &
                <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent block">
                  Conditions
                </span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Please read these terms carefully before using DomainSpot.
                They govern your use of our platform and services.
              </p>
              <p className="text-sm text-muted-foreground">
                Last updated: January 15, 2025
              </p>
            </div>
          </div>
        </section>

        {/* Terms Sections */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto space-y-8">
              {sections.map((section, index) => (
                <Card key={index} className="border-0 shadow-soft">
                  <CardContent className="p-8">
                    <div className="flex items-start gap-4 mb-6">
                      <div className="p-3 rounded-lg bg-primary/10">
                        <section.icon className="h-6 w-6 text-primary" />
                      </div>
                      <h2 className="text-2xl font-bold">{section.title}</h2>
                    </div>
                    <ul className="space-y-3">
                      {section.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start gap-3">
                          <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                          <span className="text-muted-foreground">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Terms */}
        <section className="py-16 bg-card">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto space-y-8">
              <Card className="border-0 shadow-soft">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Payment & Billing</h2>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      <strong>Subscription Fees:</strong> All fees are charged in advance on a monthly or annual basis.
                      Prices are subject to change with 30 days notice to existing subscribers.
                    </p>
                    <p>
                      <strong>Refunds:</strong> We offer a 14-day money-back guarantee for new subscribers.
                      Refunds are processed within 5-7 business days to the original payment method.
                    </p>
                    <p>
                      <strong>Cancellation:</strong> You may cancel your subscription at any time.
                      Cancellation takes effect at the end of your current billing period.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Intellectual Property</h2>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      <strong>Our Content:</strong> All content, features, and functionality of DomainSpot
                      are owned by us and protected by copyright, trademark, and other intellectual property laws.
                    </p>
                    <p>
                      <strong>Your Data:</strong> You retain ownership of any data you provide to our platform.
                      We only use your data as described in our Privacy Policy.
                    </p>
                    <p>
                      <strong>License:</strong> We grant you a limited, non-exclusive license to use our platform
                      for your personal or business domain investment activities.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Termination</h2>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      <strong>By You:</strong> You may terminate your account at any time by canceling your subscription
                      or contacting our support team.
                    </p>
                    <p>
                      <strong>By Us:</strong> We may terminate or suspend your account if you violate these terms,
                      engage in fraudulent activity, or for other legitimate business reasons.
                    </p>
                    <p>
                      <strong>Effect of Termination:</strong> Upon termination, your access to the platform will cease,
                      but these terms will continue to apply to any prior use of our services.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Legal Information */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <Card className="border-0 shadow-soft bg-gradient-to-br from-primary/5 to-primary/10">
                <CardContent className="p-8">
                  <div className="text-center">
                    <FileText className="h-12 w-12 text-primary mx-auto mb-4" />
                    <h2 className="text-2xl font-bold mb-4">Governing Law</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        These Terms of Service are governed by and construed in accordance with the laws
                        of the State of California, United States, without regard to conflict of law principles.
                      </p>
                      <p>
                        Any disputes arising from these terms or your use of DomainSpot will be resolved
                        through binding arbitration in San Francisco, California.
                      </p>
                      <p>
                        If any provision of these terms is found to be unenforceable, the remaining provisions
                        will continue in full force and effect.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-8 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <p className="text-sm text-muted-foreground mb-2">
                Questions about these terms? Contact our legal team:
              </p>
              <p className="font-medium"><EMAIL></p>
            </div>
          </div>
        </section>
      </main>
      <NewFooter />
    </div>
  );
};

export default Terms;
